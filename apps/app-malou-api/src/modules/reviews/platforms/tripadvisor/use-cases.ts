import { sampleSize } from 'lodash';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform, IReview } from '@malou-io/package-models';
import { isFulfilled, MalouErrorCode, PlatformKey, PostedStatus, TimeInSeconds } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import { fetchUntilWorks } from ':microservices/node-crawler';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import {
    getBackupReviewsQuery,
    getOriginalTextsAndTitlesQuery,
    getPreferredReviewsQuery,
} from ':modules/providers/platforms/tripadvisor/tripadvisor.queries';
import { PlatformHeaderConfigOptions, replyToReview } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import {
    TripAdvisorApiResponseBackupApproach,
    TripAdvisorApiResponseOriginalTexts,
    TripAdvisorApiResponsePreferredApproach,
    TripAdvisorRawReview,
    TripAdvisorRawReviewBackupApproach,
    TripAdvisorRawReviewPreferredApproach,
} from ':modules/reviews/platforms/tripadvisor/interfaces';
import {
    extractBusinessId,
    TripadvisorReply,
    TripadvisorReplyPayload,
    TripadvisorReview,
} from ':modules/reviews/platforms/tripadvisor/tripadvisor-review-mapper';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { Cache } from ':plugins/cache';
import { SlackChannel, SlackService } from ':services/slack.service';

export const MAX_TRIPADVISOR_REPLY_RETRIES = 20;

@autoInjectable()
export default class TripadvisorReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        @inject(SlackService) private readonly _slackService: SlackService,
        @inject(InjectionToken.Cache) private readonly _cache: Cache,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async getReviewsData({ restaurantId }: { restaurantId?: string }, recentOnly?: boolean): Promise<TripadvisorReview[]> {
        assert(restaurantId, 'Missing restaurantId');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TRIPADVISOR);
        const socialId = platform?.socialId;
        assert(socialId, 'Missing socialId');

        const DEFAULT_REVIEW_LIMIT = 20;
        const RECENT_ONLY_REVIEW_COUNT = 60;
        const FULL_REVIEW_COUNT = 1500;
        const maxOffset = recentOnly ? RECENT_ONLY_REVIEW_COUNT : FULL_REVIEW_COUNT;
        const reviewsPerPage = DEFAULT_REVIEW_LIMIT;

        const promises: Promise<TripadvisorReview[]>[] = [];

        for (let offset = 0; offset <= maxOffset; offset += reviewsPerPage) {
            promises.push(this._fetchReviewsWithFallback(socialId, offset, reviewsPerPage));
        }

        const allResults = await Promise.allSettled(promises);
        const validResults = allResults.filter(isFulfilled).map((r) => r.value);
        const failedResults = allResults.filter(isRejected).map((r) => r.reason);

        if (validResults.length === 0) {
            logger.warn('[FAILED_TRIP_REVIEWS] No valid results there is an issue with the fetch', { failedResults, socialId });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
            throw new Error(failedResults?.[0]?.message ?? 'No valid results there is an issue with the fetch');
        }

        if (failedResults.length) {
            logger.error('[FAILED_TRIP_REVIEWS] Some requests failed', { failedResults, socialId });
        }

        reviewsFetchCounter.add(1, {
            source: PlatformKey.TRIPADVISOR,
            status: 'success',
        });

        const allReviews = validResults.flat();

        return allReviews;
    }

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    /**
     * Update Reviews in malou database with new review reply
     */
    async reply({
        review,
        comment,
        headerConfig,
        retryReplyingToOtherReviews = true,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        headerConfig?: PlatformHeaderConfigOptions;
        retryReplyingToOtherReviews?: boolean;
    }): Promise<TripadvisorReply & ({} | { error: any; review: IReview })> {
        const tripadvisorComment = comment as TripadvisorReplyPayload;
        let locationId: number | null = null;
        try {
            assert(review.socialLink, 'Missing socialLink on review');
            locationId = extractBusinessId(review.socialLink);
            await replyToReview(PlatformKey.TRIPADVISOR, { review, comment, locationId }, { headerConfig });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'success',
            });
        } catch (error) {
            logger.warn('[ERROR_REPLY_REVIEW_TRIPADVISOR] Error when replying to review, set comment to RETRY', {
                review,
                comment,
                locationId,
                error,
            });
            return { comment: tripadvisorComment.comment, posted: PostedStatus.RETRY, error, review };
        }
        if (retryReplyingToOtherReviews) {
            await this.retryReplyingSomeReviews(headerConfig);
        }
        return { comment: tripadvisorComment.comment, posted: PostedStatus.PENDING };
    }

    pushReviewComment = ({ socialId, key, comment }) => this.reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    updateComment = async function () {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    retryReplyingSomeReviews = async (headerConfig?: PlatformHeaderConfigOptions, reviewsSampleSize = 10): Promise<void> => {
        const reviews = await this.reviewsRepository.getReviewsWithCommentsToRetryReplying(PlatformKey.TRIPADVISOR);
        if (!reviews.length) {
            return;
        }
        const reviewsSample = sampleSize(reviews, reviewsSampleSize);
        const promises = reviewsSample.map(async (review) => this._updateReviewComment(review, headerConfig)).filter(Boolean);
        const results = await Promise.all(promises);
        if (results.length) {
            logger.info(`[RETRY_REPLYING_REVIEWS_TRIPADVISOR] ${results.length} reviews updated`, { results });
        }
        return;
    };

    private _updateReviewComment = async (review: IReview, headerConfig?: PlatformHeaderConfigOptions): Promise<IReview | undefined> => {
        const commentToRetryPosting = review.comments.find((comment) => comment.posted === PostedStatus.RETRY);
        if (!commentToRetryPosting) {
            return;
        }
        const result = await this.reply({
            review,
            comment: { comment: commentToRetryPosting.text },
            headerConfig,
            retryReplyingToOtherReviews: false,
        });
        const currentRetries = commentToRetryPosting.retries ?? 0;
        const retries = result.posted === PostedStatus.RETRY ? currentRetries + 1 : currentRetries;
        const posted = retries > MAX_TRIPADVISOR_REPLY_RETRIES ? PostedStatus.REJECTED : result.posted;

        if (result.posted === PostedStatus.RETRY && posted === PostedStatus.REJECTED) {
            const { name } = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: review.restaurantId },
                options: { lean: true },
                projection: { name: 1 },
            });
            this._slackService.sendAlert({
                channel: SlackChannel.REVIEWS_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.PLATFORM_PUBLISH_ERROR, {
                        message: 'Could not publish reply to Tripadvisor review',
                        metadata: { socialId: review.socialId },
                    }),
                    endpoint: `restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
                    metadata: {
                        description: `Reply could not be published to Tripadvisor for review with 
                        socialId ${review.socialId}`,
                        restaurantName: name,
                    },
                },
            });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
        }

        return this.reviewsRepository.updateReviewCommentStatus({
            commentId: commentToRetryPosting._id,
            posted,
            retries,
        });
    };

    private async _fetchReviewsWithFallback(socialId: string, offset: number, limit: number): Promise<TripadvisorReview[]> {
        try {
            const result = await this._crawlRestaurantReviewsPreferredApproach(socialId, offset, limit);
            assert(result, 'No result from trip preferred approach');
            return await this._mapApiResponseToReviews(result);
        } catch (error) {
            logger.warn('[TRIPADVISOR_FALLBACK] Preferred approach failed, trying backup approach', { socialId, offset, limit, error });
            try {
                const result = await this._crawlRestaurantReviewsBackupApproach(socialId, offset, limit);
                assert(result, 'No result from trip backup approach');
                return this._mapApiResponseToReviews(result);
            } catch (fallbackError) {
                logger.error('[TRIPADVISOR_FALLBACK] Both approaches failed', { socialId, offset, limit, error, fallbackError });
                throw fallbackError;
            }
        }
    }

    private async _mapApiResponseToReviews(
        apiResponse: TripAdvisorApiResponsePreferredApproach | TripAdvisorApiResponseBackupApproach
    ): Promise<TripadvisorReview[]> {
        const rawReviews =
            (apiResponse as TripAdvisorApiResponsePreferredApproach)?.data?.ReviewsProxy_getReviewListPageForLocation?.[0]?.reviews ??
            (apiResponse as TripAdvisorApiResponseBackupApproach)?.data?.locations?.[0]?.reviewListPage?.reviews ??
            [];
        if (!rawReviews?.length) {
            return [];
        }

        const translatedReviews = rawReviews.filter((r) => r.originalLanguage !== r.language);

        const originalTextsAndTitles = {};
        try {
            const textsAndTitles = await this._getOriginalTextsAndTitles(translatedReviews.map((r) => r.id));
            assert(textsAndTitles, 'No texts and titles from trip original texts');
            for (let i = 0; i < translatedReviews.length; i++) {
                originalTextsAndTitles[translatedReviews[i].id] = textsAndTitles[i];
            }
        } catch (error) {
            logger.error('[TRIPADVISOR_REVIEWS_ORIGINAL_TEXTS] Error fetching original texts and titles', { error });
        }

        const mapped = rawReviews.map((r: TripAdvisorRawReview) => ({
            id: r.id,
            title: originalTextsAndTitles[r.id]?.title || r.title,
            text: originalTextsAndTitles[r.id]?.text || r.text,
            date: r.publishedDate,
            rating: r.rating,
            lang: originalTextsAndTitles[r.id]?.title ? r.originalLanguage : r.language,
            endpoint:
                (r as TripAdvisorRawReviewPreferredApproach)?.reviewDetailPageWrapper?.reviewDetailPageRoute?.url ??
                (r as TripAdvisorRawReviewBackupApproach)?.url,
            answered: r.mgmtResponse?.id
                ? {
                      date: r.mgmtResponse?.publishedDate,
                      answer: r.mgmtResponse?.text,
                      author: {
                          name: r.mgmtResponse?.username,
                      },
                  }
                : undefined,
            profileName: r.userProfile?.displayName || r.username,
            mediaUrls: r.photos?.map((p) => this._getUrlFromTemplate(p.photo.photoSizeDynamic.urlTemplate)),
        }));
        return mapped;
    }

    /**
     * Crawl TripAdvisor reviews using preferred approach
     * Should return all possible reviews
     * Returns some reviews translated
     */
    private async _crawlRestaurantReviewsPreferredApproach(
        socialId: string,
        offset: number,
        limit: number = 20
    ): Promise<TripAdvisorApiResponsePreferredApproach | undefined> {
        const body = getPreferredReviewsQuery(socialId, offset, limit);

        const result = await fetchUntilWorks<TripAdvisorApiResponsePreferredApproach>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;', // Looks mandatory but random value seems to work
                },
                body,
            },
            isResponseValid: (res) => !!res?.data?.ReviewsProxy_getReviewListPageForLocation,
            retries: 2,
            crawlerCount: 2,
        });

        logger.info('[TRIPADVISOR_REVIEWS_PREFERRED_APPROACH] Response received', { socialId, offset, limit });
        return result;
    }

    /**
     * Crawl TripAdvisor reviews using backup approach
     * Some reviews might not be returned with this method, there can be holes in the racket
     */
    private async _crawlRestaurantReviewsBackupApproach(
        socialId: string,
        offset: number,
        limit: number = 20
    ): Promise<TripAdvisorApiResponseBackupApproach | undefined> {
        const body = getBackupReviewsQuery(socialId, offset, limit);

        const result = await fetchUntilWorks<TripAdvisorApiResponseBackupApproach>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;', // Looks mandatory but random value seems to work
                },
                body,
            },
            isResponseValid: (res) => !!res?.data?.locations?.[0]?.reviewListPage?.reviews,
            retries: 2,
            crawlerCount: 2,
        });

        logger.info('[TRIPADVISOR_REVIEWS_BACKUP_APPROACH] Response received', { socialId, offset, limit });
        return result;
    }

    /**
     * When using preferred approach we might get translated reviews
     * This method is used to get the original text and title of the review
     */
    private async _getOriginalTextsAndTitles(reviewIds: number[]): Promise<{ text: string; title: string }[]> {
        // Use cache to prevent fetching all the time
        const foundInCache = await Promise.all(reviewIds.map((id) => this._cache.get(`tripadvisor-original-text-${id}`)));
        const notFoundInCacheIndices = foundInCache.map((r, i) => (r === null ? i : null)).filter((i) => i !== null) as number[];
        if (!notFoundInCacheIndices.length) {
            return foundInCache as { text: string; title: string }[];
        }
        const notFoundInCache = reviewIds.filter((_, i) => foundInCache[i] === null);

        const body = getOriginalTextsAndTitlesQuery(notFoundInCache);

        const result = await fetchUntilWorks<TripAdvisorApiResponseOriginalTexts>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;',
                },
                body,
            },
            isResponseValid: (res) => !!res?.data?.ReviewsProxy_getBestLanguageMatchReviewById,
            retries: 2,
            crawlerCount: 2,
        });
        assert(result, 'No result from trip original texts');
        const byReviewId = new Map<number, { text: string; title: string }>();
        await Promise.all(
            result.data.ReviewsProxy_getBestLanguageMatchReviewById.map((r, i) => {
                const reviewId = reviewIds[notFoundInCacheIndices[i]];
                byReviewId.set(reviewId, r.reviews);
                return this._cache.set(`tripadvisor-original-text-${reviewId}`, r.reviews, TimeInSeconds.DAY * 7);
            })
        );

        return foundInCache.map((r, i) => r ?? byReviewId.get(reviewIds[i]));
    }

    private _getUrlFromTemplate(template: string): string {
        //example of urlTemplate : "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/30/f7/de/1e/caption.jpg?w={width}&h={height}&s=1"
        return template.replace('{width}', '400').replace('{height}', '400');
    }
}
