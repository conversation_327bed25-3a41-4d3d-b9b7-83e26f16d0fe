import { singleton } from 'tsyringe';

import { PlatformDataFetchedStatus, PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { send as sendFetchPlatformReviewsMessage } from ':modules/reviews/queues/update-reviews/update-reviews.producer';

@singleton()
export class RefreshStalePlatformReviewsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private _platformsRepository: PlatformsRepository
    ) {}

    async execute(platformKey: PlatformKey, recentOnly: boolean = true): Promise<void> {
        const currentStateStatusKey = `currentState.reviews.fetched.${platformKey}.status`;
        const currentStateLastTriedKey = `currentState.reviews.fetched.${platformKey}.lastTried`;

        const parallelPlatformCountToRefresh = 10;

        const staleRestaurants = await this._restaurantsRepository.find({
            filter: {
                active: true,
                [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
            },
            projection: { _id: 1 },
            options: {
                lean: true,
                sort: { [currentStateLastTriedKey]: 1 },
                limit: parallelPlatformCountToRefresh,
            },
        });

        const platformsToRefresh = await this._platformsRepository.find({
            filter: {
                key: platformKey,
                restaurantId: { $in: staleRestaurants.map((r) => r._id) },
            },
            projection: { _id: 1 },
            options: { lean: true },
        });

        await Promise.all(platformsToRefresh.map(({ _id }) => sendFetchPlatformReviewsMessage(_id, recentOnly)));
    }
}
