import { shuffle } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { PlatformDataFetchedStatus, PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { send as sendFetchPlatformReviewsMessage } from ':modules/reviews/queues/update-reviews/update-reviews.producer';

@singleton()
export class StartReviewsCatchUpUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private _platformsRepository: PlatformsRepository
    ) {}

    async execute(platformKey: PlatformKey): Promise<void> {
        const fetchCountForPlatform: number | undefined = {
            [PlatformKey.LAFOURCHETTE]: 6,
        }[platformKey];
        assert(platformKey);
        const currentStateKey = `currentState.reviews.fetched.${platformKey}.status`;
        const restaurantsToCatchUp = await this._restaurantsRepository.find({
            filter: {
                active: true,
                [currentStateKey]: {
                    $in: [PlatformDataFetchedStatus.ERROR, PlatformDataFetchedStatus.PENDING, PlatformDataFetchedStatus.ASYNC],
                },
            },
            projection: { _id: 1 },
            options: { lean: true },
        });
        const platformsToCatchUp = await this._platformsRepository.find({
            filter: { key: platformKey, restaurantId: { $in: restaurantsToCatchUp.map((r) => r._id) } },
            projection: { _id: 1 },
            options: { lean: true },
        });

        await Promise.all(
            shuffle(platformsToCatchUp)
                ?.slice(0, fetchCountForPlatform)
                .map(({ _id }) => sendFetchPlatformReviewsMessage(_id, true))
        );
    }
}
