import { IReview } from '@malou-io/package-models';

export enum TripadvisorQueryIds {
    REPLY_QUERY_ID = '4a0e20d2c37e7b20',
    GET_REVIEWS_PAGINATED_QUERY_ID = 'd95bc0f3f8495b9b',
    PREFERRED_GET_REVIEWS_PAGINATED_QUERY_ID = '9365c2244f5b46a6',
    BACKUP_GET_REVIEWS_PAGINATED_QUERY_ID = 'aaff0337570ed0aa',
    GET_ORIGINAL_TEXT_AND_TITLE_QUERY_ID = '55ab9595a36e6548',
}

export const DEFAULT_TRIPADVISOR_PAGE_SIZE = 30;

export const getReplyQuery = (review: IReview, comment: any, locationId: number) => {
    const socialId = +review.socialId;
    return {
        extensions: {
            preRegisteredQueryId: TripadvisorQueryIds.REPLY_QUERY_ID,
        },
        query: TripadvisorQueryIds.REPLY_QUERY_ID,
        variables: {
            request: {
                globalId: `taur:${review.socialId}`,
                locationId,
                responseId: 'undefined', // Default value when no comments on the review
                textReviewResponse: null, // Default value when no comments on the review ?
                taPostReviewInfo: {
                    language: 'fr',
                    locationId,
                    reviewId: socialId,
                    text: comment.comment,
                    username: 'Direction',
                    connectionToSubject: 'Propriétaire',
                    submissionDomain: 'tripadvisor.fr',
                    cookie: 'web331a.*************.189CF81D4C1',
                },
            },
        },
    };
};

export const getPaginatedReviewsQuery = (locationId: number, page: number) => {
    const pageOffset = page * DEFAULT_TRIPADVISOR_PAGE_SIZE;
    return {
        query: TripadvisorQueryIds.GET_REVIEWS_PAGINATED_QUERY_ID,
        variables: {
            locationId,
            targetedReviewId: null,
            pageSize: DEFAULT_TRIPADVISOR_PAGE_SIZE,
            pageOffset,
            filters: [
                {
                    axis: 'PROVIDER',
                    selections: ['TA'],
                }, // Only tripadvisor reviews
                {
                    axis: 'RESPONSE_STATUS',
                    selections: ['NONE'],
                }, // Only reviews without response
            ],
            dateFilter: {
                minTime: '',
            },
            countOptions: [
                {
                    axis: 'RESPONSE_STATUS',
                    selections: ['PENDING'],
                },
            ],
        },
    };
};

/**
 * Query for preferred approach to get TripAdvisor reviews
 * Should return all possible reviews
 * Returns some reviews translated
 */
export const getPreferredReviewsQuery = (socialId: string, offset: number, limit: number = 20) => {
    return {
        variables: {
            locationId: Number(socialId),
            filters: [],
            limit: limit,
            offset: offset,
            sortType: null,
            sortBy: 'SERVER_DETERMINED',
        },
        extensions: {
            preRegisteredQueryId: TripadvisorQueryIds.PREFERRED_GET_REVIEWS_PAGINATED_QUERY_ID,
        },
    };
};

/**
 * Query for backup approach to get TripAdvisor reviews
 * Some reviews might not be returned with this method, there can be holes in the racket
 */
export const getBackupReviewsQuery = (socialId: string, offset: number, limit: number = 20) => {
    return {
        variables: {
            locationId: Number(socialId),
            offset: offset,
            limit: limit,
            keywordVariant: 'location_keywords_v2_llr_order_30_fr',
            needKeywords: false,
            language: 'fr',
            filters: [
                {
                    axis: 'SORT',
                    selections: ['mostRecent'],
                },
            ],
            prefs: {
                showMT: false,
                sortBy: 'DATE',
                sortType: '',
            },
            initialPrefs: {
                showMT: false,
                sortBy: 'DATE',
                sortType: '',
            },
        },
        extensions: {
            preRegisteredQueryId: TripadvisorQueryIds.BACKUP_GET_REVIEWS_PAGINATED_QUERY_ID,
        },
    };
};

/**
 * Query to get original text and title for translated reviews
 * When using preferred approach we might get translated reviews
 * This method is used to get the original text and title of the review
 */
export const getOriginalTextsAndTitlesQuery = (reviewIds: number[]) => {
    return {
        variables: {
            reviewsIds: reviewIds,
        },
        extensions: {
            preRegisteredQueryId: TripadvisorQueryIds.GET_ORIGINAL_TEXT_AND_TITLE_QUERY_ID,
        },
    };
};
