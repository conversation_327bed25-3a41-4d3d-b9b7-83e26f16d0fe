import { inject, Injectable } from '@angular/core';
import { capitalize, orderBy } from 'lodash';
import { DateTime } from 'luxon';
import { catchError, EMPTY, forkJoin, map, Observable, of } from 'rxjs';

import {
    AggregationTimeScale,
    getPlatformKeysWithRating,
    getPlatformKeysWithReview,
    getPositiveAndNegativeStatsForSemanticAnalysis,
    getSegmentsForSemanticAnalysisStats,
    MalouComparisonPeriod,
    MalouMetric,
    PlatformKey,
} from '@malou-io/package-utils';

import { StarValue } from ':core/constants';
import { ExperimentationService } from ':core/services/experimentation.service';
import { SegmentAnalysesService } from ':core/services/segment-analyses.service';
import { LocalStorage } from ':core/storage/local-storage';
import { ReviewsByAnswerStatus } from ':modules/reviews/reviews.interface';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { InsightsService } from ':modules/statistics/insights.service';
import { InsightsByPlatform } from ':shared/models';
import { SummarySectionsData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

interface PlatformWithRatingValue {
    platformKey: string;
    value: number;
}

const DEFAULT_REVIEWS_RATING = {
    [StarValue.UNKNOWN]: null,
    [StarValue.ONE]: null,
    [StarValue.TWO]: null,
    [StarValue.THREE]: null,
    [StarValue.FOUR]: null,
    [StarValue.FIVE]: null,
};

@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsEReputationSectionService {
    private readonly _insightsService: InsightsService = inject(InsightsService);
    private readonly _reviewsService: ReviewsService = inject(ReviewsService);
    private readonly _segmentAnalysesService: SegmentAnalysesService = inject(SegmentAnalysesService);
    private readonly _experimentationService: ExperimentationService = inject(ExperimentationService);

    execute(
        filters: SummarySectionsDataFilters['eReputation']
    ): Observable<{ current: SummarySectionsData['eReputation'] | null; previous: SummarySectionsData['eReputation'] | null }> {
        const platformKeysWithReview: string[] = getPlatformKeysWithReview();
        const platformKeysWithRating: string[] = getPlatformKeysWithRating();
        const platformKeysToUse = filters.platformKeys.filter((platform: PlatformKey) => platformKeysWithReview.includes(platform));
        const connectedPlatformsWithRating: PlatformKey[] = platformKeysToUse.filter((platform: string) =>
            platformKeysWithRating.includes(platform)
        );
        const connectedPlatformsWithReview: PlatformKey[] = platformKeysToUse.filter((platform: string) =>
            platformKeysWithReview.includes(platform)
        );
        const restaurantId = filters.restaurantId;
        const startDate = filters.startDate;
        const endDate = filters.endDate;
        if (!startDate || !endDate) {
            return EMPTY;
        }

        const startDateLuxon = DateTime.fromJSDate(startDate);
        const endDateLuxon = DateTime.fromJSDate(endDate);
        const periodInDays = endDateLuxon.diff(startDateLuxon, 'days').days;
        const previousStartDate = startDateLuxon.minus({ days: periodInDays }).toJSDate();
        const previousEndDate = startDateLuxon.toJSDate();

        return forkJoin([
            // Platforms rating
            this._insightsService
                .getInsights({
                    restaurantIds: [restaurantId],
                    startDate,
                    endDate,
                    platformKeys: connectedPlatformsWithRating,
                    metrics: [MalouMetric.PLATFORM_RATING],
                    aggregators: [AggregationTimeScale.BY_DAY],
                })
                .pipe(
                    map((res) => res?.data[restaurantId]),
                    map((res) => this._getLatestRatingForEachPlatformFromPeriod(res)),
                    catchError(() => of([]))
                ),
            this._insightsService
                .getInsights({
                    restaurantIds: [restaurantId],
                    startDate: filters.startDate,
                    endDate: filters.endDate,
                    platformKeys: connectedPlatformsWithRating,
                    metrics: [MalouMetric.PLATFORM_RATING],
                    aggregators: [AggregationTimeScale.BY_DAY],
                    previousPeriod: true,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(
                    map((res) => res?.data[restaurantId]),
                    map((res) => this._getLatestRatingForEachPlatformFromPeriod(res)),
                    catchError(() => of([]))
                ),
            // Review count
            this._reviewsService
                .getChartRestaurantsReviewsTotal({ restaurantId, platforms: connectedPlatformsWithReview, startDate, endDate })
                .pipe(catchError(() => of(null))),
            this._reviewsService
                .getChartRestaurantsReviewsTotal({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(catchError(() => of(null))),
            // Review answer rate
            this._reviewsService
                .getChartRestaurantsReviewsReplied({ restaurantId, platforms: connectedPlatformsWithReview, startDate, endDate })
                .pipe(catchError(() => of(null))),
            this._reviewsService
                .getChartRestaurantsReviewsReplied({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(catchError(() => of(null))),
            // Review answer time
            this._reviewsService
                .getChartAverageAnswerTime({ restaurantId, platforms: connectedPlatformsWithReview, startDate, endDate })
                .pipe(catchError(() => of(null))),
            this._reviewsService
                .getChartAverageAnswerTime({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(catchError(() => of(null))),
            // Review average rating
            this._reviewsService
                .getRestaurantsReviewsAverageChartData({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                })
                .pipe(catchError(() => of(null))),
            this._reviewsService
                .getRestaurantsReviewsAverageChartData({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(catchError(() => of(null))),
            // Reviews per rating
            this._reviewsService
                .getChartRestaurantsReviewsRatings({ restaurantId, platforms: connectedPlatformsWithReview, startDate, endDate })
                .pipe(catchError(() => of(null))),
            this._reviewsService
                .getChartRestaurantsReviewsRatings({
                    restaurantId,
                    platforms: connectedPlatformsWithReview,
                    startDate,
                    endDate,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                })
                .pipe(catchError(() => of(null))),
            // Reviews Analysis
            this._reviewsService.getReviewsWithAnalysis(startDate, endDate, connectedPlatformsWithReview, restaurantId),
            // Topics Analysis
            this._segmentAnalysesService
                .getSegmentAnalysesTopTopics({
                    startDate,
                    endDate,
                    keys: filters.platformKeys,
                    restaurantId,
                })
                .pipe(map((res) => res.data)),
            this._segmentAnalysesService
                .getSegmentAnalysesTopTopics({
                    startDate: previousStartDate,
                    endDate: previousEndDate,
                    keys: filters.platformKeys,
                    restaurantId,
                })
                .pipe(map((res) => res.data)),
        ]).pipe(
            map(
                ([
                    currentPlatformRating,
                    previousPlatformRatings,
                    currentReviewCount,
                    previousReviewCount,
                    currentReviewsAnswerRate,
                    previousReviewsAnswerRate,
                    currentReviewAnswerTime,
                    previousReviewAnswerTime,
                    currentReviewAverageRating,
                    previousReviewAverageRating,
                    currentReviewsPerRating,
                    previousReviewsPerRating,
                    reviewsAnalysis,
                    topicsAnalysis,
                    previousTopicsAnalysis,
                ]) => {
                    const currentNotes = currentPlatformRating
                        .map((platform) => ({
                            platform: platform.platformKey,
                            note: platform.value,
                        }))
                        .filter((note) => !!note.note);
                    const previousNotes = previousPlatformRatings
                        .map((platform) => ({
                            platform: platform.platformKey,
                            note: platform.value,
                        }))
                        .filter((note) => !!note.note);

                    const currentReviews = {
                        totalCount: currentReviewCount?.results?.[0]?.total ?? null,
                        averageRating: currentReviewAverageRating?.results?.[0]?.averageRating ?? null,
                    };
                    const previousReviews = {
                        totalCount: previousReviewCount?.results?.[0]?.total ?? null,
                        averageRating: previousReviewAverageRating?.results?.[0]?.averageRating ?? null,
                    };

                    const currentReviewsRating = {
                        ...DEFAULT_REVIEWS_RATING,
                    };
                    const previousReviewsRating = {
                        ...DEFAULT_REVIEWS_RATING,
                    };

                    const totalReviewCount =
                        currentReviewsPerRating?.results?.reduce((acc, result) => {
                            acc += result.total;
                            return acc;
                        }, 0) ?? 0;

                    const totalPreviousReviewCount =
                        previousReviewsPerRating?.results?.reduce((acc, result) => {
                            acc += result.total;
                            return acc;
                        }, 0) ?? 0;

                    if (totalReviewCount !== 0) {
                        currentReviewsPerRating?.results?.reduce((acc, result) => {
                            acc[result.rating] = (result.total * 100) / totalReviewCount;
                            return acc;
                        }, currentReviewsRating);
                    }

                    if (totalPreviousReviewCount !== 0) {
                        previousReviewsPerRating?.results?.reduce((acc, result) => {
                            acc[result.rating] = (result.total * 100) / totalPreviousReviewCount;
                            return acc;
                        }, previousReviewsRating);
                    }

                    const currentReviewsResponse = {
                        rate: this._getAnswerRate(currentReviewsAnswerRate),
                        averageTime: currentReviewAnswerTime,
                    };

                    const previousReviewsResponse = {
                        rate: this._getAnswerRate(previousReviewsAnswerRate),
                        averageTime: previousReviewAnswerTime,
                    };

                    const isSemanticAnalysisFeatureEnabled =
                        this._experimentationService.isFeatureEnabledForRestaurant('release-new-semantic-analysis');
                    const currentSentimentsPercentage = getPositiveAndNegativeStatsForSemanticAnalysis(
                        getSegmentsForSemanticAnalysisStats(reviewsAnalysis, isSemanticAnalysisFeatureEnabled)
                    );
                    const previousSentimentsPercentage = getPositiveAndNegativeStatsForSemanticAnalysis(
                        getSegmentsForSemanticAnalysisStats(reviewsAnalysis, isSemanticAnalysisFeatureEnabled)
                    );

                    const lang = LocalStorage.getLang();
                    // topics analysis
                    const currentTop3Topics = orderBy(topicsAnalysis.positiveTopics ?? [], 'positiveCount', 'desc').map((topic) =>
                        capitalize(topic.translations?.[lang] ?? undefined)
                    );
                    const currentFlop3Topics = orderBy(topicsAnalysis.negativeTopics ?? [], 'negativeCount', 'desc').map((topic) =>
                        capitalize(topic.translations?.[lang] ?? undefined)
                    );
                    const previousTop3Topics = orderBy(previousTopicsAnalysis.positiveTopics ?? [], 'positiveCount', 'desc').map((topic) =>
                        capitalize(topic.translations?.[lang] ?? undefined)
                    );
                    const previousFlop3Topics = orderBy(previousTopicsAnalysis.negativeTopics ?? [], 'negativeCount', 'desc').map((topic) =>
                        capitalize(topic.translations?.[lang] ?? undefined)
                    );

                    return {
                        current: {
                            notes: currentNotes,
                            reviews: currentReviews,
                            reviewsRatings: currentReviewsRating,
                            reviewsResponse: currentReviewsResponse,
                            sentimentsPercentage: {
                                positive: currentSentimentsPercentage.positiveSentimentsPercentage,
                                negative: currentSentimentsPercentage.negativeSentimentsPercentage,
                            },
                            top3Subjects: currentTop3Topics.slice(0, 3) ?? null,
                            flop3Subjects: currentFlop3Topics.slice(0, 3) ?? null,
                        },
                        previous: {
                            notes: previousNotes,
                            reviews: previousReviews,
                            reviewsRatings: previousReviewsRating,
                            reviewsResponse: previousReviewsResponse,
                            sentimentsPercentage: {
                                positive: previousSentimentsPercentage.positiveSentimentsPercentage,
                                negative: previousSentimentsPercentage.negativeSentimentsPercentage,
                            },
                            top3Subjects: previousTop3Topics.slice(0, 3) ?? null,
                            flop3Subjects: previousFlop3Topics.slice(0, 3) ?? null,
                        },
                    };
                }
            ),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _getLatestRatingForEachPlatformFromPeriod(platformsInsights?: InsightsByPlatform): PlatformWithRatingValue[] {
        if (!platformsInsights) {
            return [];
        }
        return Object.keys(platformsInsights)?.map((platformKey) => {
            const platformInsightsByDayRatings =
                platformsInsights?.[platformKey]?.[AggregationTimeScale.BY_DAY]?.[MalouMetric.PLATFORM_RATING];
            if (!platformInsightsByDayRatings) {
                return { platformKey, value: null };
            }
            const platformInsightsByDayRatingsCleaned = platformInsightsByDayRatings
                .sort((a, b) => new Date(b.date)?.getTime() - new Date(a.date)?.getTime())
                .filter((rating) => !!rating.value);
            if (!platformInsightsByDayRatingsCleaned[0]?.value) {
                return { platformKey, value: null };
            }
            return {
                platformKey,
                value: platformInsightsByDayRatingsCleaned[0].value,
            };
        });
    }

    private _getAnswerRate(reviewAnswerRate: ReviewsByAnswerStatus | null): number | null {
        if (!reviewAnswerRate) {
            return null;
        }
        const totalReviews = reviewAnswerRate.results.answered.length + reviewAnswerRate.results.notAnswered.length;
        if (totalReviews === 0) {
            return null;
        }
        const totalAnswered = reviewAnswerRate.results.answered.length;
        return (totalAnswered * 100) / totalReviews;
    }
}
