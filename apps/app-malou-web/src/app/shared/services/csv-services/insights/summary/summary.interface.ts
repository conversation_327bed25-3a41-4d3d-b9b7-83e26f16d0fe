import { MonthAndYear, PlatformKey } from '@malou-io/package-utils';

import { StarValue } from ':core/constants';
import { LightNfc } from ':shared/models';

export interface SummarySectionsDataFilters {
    restaurantName: string;
    startDate: Date;
    endDate: Date;
    seo: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        startMonthYear: MonthAndYear;
        endMonthYear: MonthAndYear;
    };
    eReputation: {
        startDate: Date;
        endDate: Date;
        restaurantId: string;
        platformKeys: PlatformKey[];
    };
    booster: {
        startDate: Date;
        endDate: Date;
        nfcs: LightNfc[];
    };
    socialMedia: {
        restaurantId: string;
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
        restaurantCreatedAt?: Date;
    };
    roi: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        nbMonths: number;
    };
}
export interface SummarySectionsData {
    seo: {
        impressions: {
            totalAppearanceMaps: number | null;
            totalAppearanceSearch: number | null;
            totalAppearance: number | null;
        };
        actions: {
            totalActions: number | null;
            conversionRate: number | null;
            totalCallCount: number | null;
            totalDirectionCount: number | null;
            totalWebsiteVisitCount: number | null;
            totalMenuClickCount: number | null;
            totalBookingClickCount: number | null;
            totalOrderClickCount: number | null;
        };
        keywordsInTop20Count: number | null;
        totalNotorietySearchCount: number | null;
        totalDiscoverySearchCount: number | null;
        top3KeywordsNotoriety: string[] | null;
        top3KeywordsDiscovery: string[] | null;
        keywords: {
            keyword: string;
            impressionCount: number | null;
            evolution?: number | null;
        }[];
        googlePostCount: number | null;
    } | null;
    eReputation: {
        notes: {
            platform: PlatformKey;
            note: number | null;
        }[];
        reviews: {
            totalCount: number | null;
            averageRating: number | null;
        };
        reviewsRatings: {
            [key in StarValue]: number | null;
        };
        reviewsResponse: {
            rate: number | null;
            averageTime: number | null;
        };
        sentimentsPercentage: {
            positive: number | null;
            negative: number | null;
        };
        top3Subjects: string[] | null;
        flop3Subjects: string[] | null;
    } | null;
    booster: {
        totalTotemScanCount: number | null;
        totalWofScanCount: number | null;
        gainedPrivateReviewCount: number | null;
        gainedPublicReviewCount: number | null;
        winnerCount: number | null;
        giftCount: number | null;
        gainedReviewCountPerRating: Record<StarValue, number | null>;
        totemCountEvolution: number | null;
        topTotems: string[] | null;
    } | null;
    socialMedia: {
        followerCount: number | null;
        engagementRate: number | null;
        impressionCount: number | null;
        postCount: number | null;
    } | null;
    roi: {
        additionalClientCount: {
            min: number | null;
            max: number | null;
        };
        additionalSalesRevenue: {
            min: number | null;
            max: number | null;
        };
        savedTime: number | null;
        performanceScore: number | null;
    } | null;
}

export interface SummaryCsvData {
    current: SummarySectionsData;
    previous: SummarySectionsData;
    startDate: string;
    endDate: string;
    previousStartDate: string;
    previousEndDate: string;
    restaurantName: string;
}

export type SummarySectionsTranslationKeys = keyof SummarySectionsData;

export enum SummarySection {
    SEO = 'seo',
    E_REPUTATION = 'eReputation',
    BOOSTER = 'booster',
    SOCIAL_MEDIA = 'socialMedia',
    ROI = 'roi',
}
