import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { DateTime } from 'luxon';
import { combineLatest, filter, forkJoin, map, Observable, of, switchMap } from 'rxjs';

import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import * as PlatformsReducer from ':modules/platforms/store/platforms.reducer';
import { StatisticsFiltersContext } from ':modules/statistics/filters/filters.context';
import { selectFilters } from ':modules/statistics/store/statistics.selectors';
import { LightNfc, Restaurant } from ':shared/models';
import { AbstractCsvService, CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';
import { DataToCsvRowMapperService } from ':shared/services/csv-services/insights/summary/data-csv-row-mapper';
import { SummaryCsvInsightsBoosterSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/booster-data-section.service';
import { SummaryCsvInsightsEReputationSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/e-reputation-data-section.service';
import { SummaryCsvInsightsRoiSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/roi-data-section.service';
import { SummaryCsvInsightsSeoSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/seo-data-section.service';
import { SummaryCsvInsightsSocialMediaSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/social-media-data-section.service';
import { SummaryCsvData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

interface SummaryCsvInsightsOptions {
    startDate: Date;
    endDate: Date;
}
@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsService extends AbstractCsvService<SummaryCsvData, SummaryCsvInsightsOptions> {
    constructor(
        private readonly _store: Store,
        private readonly _dataToCsvRowMapperService: DataToCsvRowMapperService,
        private readonly _restaurantsService: RestaurantsService,
        private readonly _summaryCsvInsightsSeoSectionService: SummaryCsvInsightsSeoSectionService,
        private readonly _summaryCsvInsightsEReputationSectionService: SummaryCsvInsightsEReputationSectionService,
        private readonly _summaryCsvInsightsBoosterSectionService: SummaryCsvInsightsBoosterSectionService,
        private readonly _statisticsFiltersContext: StatisticsFiltersContext,
        private readonly _summaryCsvInsightsSocialMediaSectionService: SummaryCsvInsightsSocialMediaSectionService,
        private readonly _summaryCsvInsightsRoiSectionService: SummaryCsvInsightsRoiSectionService
    ) {
        super();
    }

    protected _isDataValid(): boolean {
        return true;
    }

    protected override _getData$(options: SummaryCsvInsightsOptions): Observable<SummaryCsvData> {
        return combineLatest([
            this._store.select(selectFilters),
            this._restaurantsService.restaurantSelected$,
            this._store.select(PlatformsReducer.selectCurrentPlatformKeys),
            this._statisticsFiltersContext.restaurantLightTotems$,
        ]).pipe(
            filter(([filters, restaurant]) => isNotNil(restaurant) && !!filters.isFiltersLoaded),
            map(([_, restaurant, platformKeys, lightNfcs]) =>
                this._getFiltersForSummarySections({
                    restaurant,
                    platformKeys,
                    lightNfcs,
                    startDate: options.startDate,
                    endDate: options.endDate,
                })
            ),
            switchMap((summarySectionsFilters) =>
                forkJoin([
                    this._summaryCsvInsightsSeoSectionService.execute(summarySectionsFilters.seo),
                    this._summaryCsvInsightsEReputationSectionService.execute(summarySectionsFilters.eReputation),
                    this._summaryCsvInsightsBoosterSectionService.execute(summarySectionsFilters.booster),
                    this._summaryCsvInsightsSocialMediaSectionService.execute(summarySectionsFilters.socialMedia),
                    this._summaryCsvInsightsRoiSectionService.execute(summarySectionsFilters.roi),
                    of(summarySectionsFilters),
                ])
            ),
            map(([seoSectionData, eReputationSectionData, boosterSectionData, socialMediaSectionData, roiSectionData, filters]) => {
                const formattedDates = this._getFormattedDates(filters);
                return {
                    startDate: formattedDates.startDate,
                    endDate: formattedDates.endDate,
                    previousStartDate: formattedDates.previousStartDate,
                    previousEndDate: formattedDates.previousEndDate,
                    restaurantName: filters.restaurantName,
                    current: {
                        seo: seoSectionData.current,
                        eReputation: eReputationSectionData.current,
                        booster: boosterSectionData.current,
                        socialMedia: socialMediaSectionData.current,
                        roi: roiSectionData.current,
                    },
                    previous: {
                        seo: seoSectionData.previous,
                        eReputation: eReputationSectionData.previous,
                        booster: boosterSectionData.previous,
                        socialMedia: socialMediaSectionData.previous,
                        roi: roiSectionData.previous,
                    },
                };
            })
        );
    }
    protected override _getCsvHeaderRow(data: SummaryCsvData): CsvAsStringArrays[0] {
        return ['Data', `${data.startDate} - ${data.endDate}`, `${data.previousStartDate} - ${data.previousEndDate}`, 'Evolution'];
    }

    protected override _getCsvDataRows(data: SummaryCsvData): CsvAsStringArrays {
        const seoRows = this._dataToCsvRowMapperService.mapSeoSectionDataToCsvRows(data.current.seo, data.previous.seo);
        const eReputationRows = this._dataToCsvRowMapperService.mapEReputationSectionDataToCsvRows(
            data.current.eReputation,
            data.previous.eReputation
        );
        const boosterRows = this._dataToCsvRowMapperService.mapBoosterSectionDataToCsvRows(data.current.booster, data.previous.booster);
        const socialMediaRows = this._dataToCsvRowMapperService.mapSocialMediaSectionDataToCsvRows(
            data.current.socialMedia,
            data.previous.socialMedia
        );
        const roiRows = this._dataToCsvRowMapperService.mapRoiSectionDataToCsvRows(data.current.roi);
        return [[data.restaurantName, '', '', ''], ...seoRows, ...eReputationRows, ...boosterRows, ...socialMediaRows, ...roiRows];
    }

    private _getFiltersForSummarySections({
        restaurant,
        platformKeys,
        startDate,
        lightNfcs,
        endDate,
    }: {
        restaurant: Restaurant | null;
        platformKeys: PlatformKey[];
        lightNfcs: LightNfc[];
        startDate: Date;
        endDate: Date;
    }): SummarySectionsDataFilters {
        const startMonthYear = {
            month: startDate.getMonth() + 1,
            year: startDate.getFullYear(),
        };
        const endMonthYear = {
            month: endDate.getMonth() + 1,
            year: endDate.getFullYear(),
        };
        const nbMonths = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate), 'months').months;
        return {
            restaurantName: restaurant?.getDisplayName() ?? '',
            startDate,
            endDate,
            seo: {
                restaurantId: restaurant!.id,
                startDate,
                endDate: DateTime.fromJSDate(endDate).minus({ days: 4 }).toJSDate(),
                endMonthYear,
                startMonthYear,
            },
            eReputation: {
                startDate,
                endDate,
                restaurantId: restaurant!.id,
                platformKeys,
            },
            booster: {
                startDate,
                endDate,
                nfcs: lightNfcs,
            },
            socialMedia: {
                restaurantId: restaurant!.id,
                // for now we only deal with instagram [@hamza]
                platformKeys: [PlatformKey.INSTAGRAM],
                startDate,
                endDate,
                restaurantCreatedAt: new Date(restaurant!.createdAt),
            },
            roi: {
                restaurantId: restaurant!.id,
                startDate,
                endDate,
                nbMonths,
            },
        };
    }

    private _getFormattedDates(
        filters: SummarySectionsDataFilters
    ): Pick<SummaryCsvData, 'startDate' | 'endDate' | 'previousStartDate' | 'previousEndDate'> {
        const startDateLuxon = DateTime.fromJSDate(filters.startDate);
        const endDateLuxon = DateTime.fromJSDate(filters.endDate);
        const periodInMonths = endDateLuxon.diff(startDateLuxon, 'months').months;

        const previousStartDate = startDateLuxon.minus({ months: Math.ceil(periodInMonths), days: 1 });
        const previousEndDate = startDateLuxon.minus({ days: 1 });

        return {
            startDate: startDateLuxon.toFormat('yyyy-MM-dd'),
            endDate: endDateLuxon.toFormat('yyyy-MM-dd'),
            previousStartDate: previousStartDate.toFormat('yyyy-MM-dd'),
            previousEndDate: previousEndDate.toFormat('yyyy-MM-dd'),
        };
    }
}
