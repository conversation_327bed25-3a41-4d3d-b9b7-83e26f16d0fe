import { Injectable } from '@angular/core';
import { DateTime } from 'luxon';
import { catchError, forkJoin, map, Observable, of } from 'rxjs';

import { StoredInsightsResponseDto } from '@malou-io/package-dto';
import {
    AggregationTimeScale,
    GeoSamplePlatform,
    getPreviousMonthYear,
    getRangeForMonthYearPeriod,
    isNotNil,
    MalouComparisonPeriod,
    MalouPeriod,
    MonthAndYear,
    MonthYearPeriod,
    PlatformKey,
    PostPublicationStatus,
} from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { PostsService } from ':core/services/posts.service';
import { InsightsService } from ':modules/statistics/insights.service';
import { ImpressionsInsightsData } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.interface';
import { KeywordSearchImpressionsService } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.service';
import { GmbInsightsChartData } from ':modules/statistics/seo/models/gmb-insight-chart-data';
import { getDayMonthYearFromDate } from ':shared/helpers';
import { GMB_METRICS } from ':shared/interfaces';
import { Keyword } from ':shared/models';
import { SummarySectionsData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsSeoSectionService {
    constructor(
        private readonly _insightsService: InsightsService,
        private readonly _keywordsService: KeywordsService,
        private readonly _keywordSearchImpressionsService: KeywordSearchImpressionsService,
        private readonly _postsService: PostsService
    ) {}

    execute(
        filters: SummarySectionsDataFilters['seo']
    ): Observable<{ current: SummarySectionsData['seo'] | null; previous: SummarySectionsData['seo'] | null }> {
        const { restaurantId, startMonthYear, endMonthYear } = filters;
        const requestBody = {
            restaurantIds: [restaurantId],
            platformKeys: [PlatformKey.GMB],
            startDate: getDayMonthYearFromDate(filters.startDate!),
            endDate: getDayMonthYearFromDate(filters.endDate!),
            metrics: GMB_METRICS.map(({ metric }) => metric),
        };
        const startDateLuxon = DateTime.fromObject(filters.startMonthYear);
        const endDateLuxon = DateTime.fromObject(filters.endMonthYear);
        const periodInMonths = endDateLuxon.diff(startDateLuxon, 'months').months;

        const startDate = startDateLuxon.startOf('month').toJSDate();
        const endDate = endDateLuxon.endOf('month').toJSDate();
        const previousStartDate = startDateLuxon.minus({ months: periodInMonths }).startOf('month').toJSDate();
        const previousEndDate = startDateLuxon.minus({ months: 1 }).endOf('month').toJSDate();
        const previousStartMonthYear = getDayMonthYearFromDate(previousStartDate);
        const previousEndMonthYear = getDayMonthYearFromDate(previousEndDate);
        // TODO (Improvement): switch to 1 call to the api [@hamza]
        return forkJoin([
            // Actions and impressions
            this._insightsService.getStoredInsights(requestBody),
            this._insightsService.getStoredInsights({
                ...requestBody,
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            }),
            // Keywords
            this._keywordsService.getKeywordsByRestaurantId(restaurantId).pipe(map((res) => res.data)),
            this._keywordsService.getKeywordRankingsForOneRestaurantV3({
                restaurantId,
                startDate,
                endDate,
                platformKey: GeoSamplePlatform.GMAPS,
            }),
            this._keywordsService.getKeywordRankingsForOneRestaurantV3({
                restaurantId,
                startDate: previousStartDate,
                endDate: previousEndDate,
                platformKey: GeoSamplePlatform.GMAPS,
            }),
            // Notoriety and discovery search
            this._keywordSearchImpressionsService.getKeywordSearchImpressionsInsights(restaurantId, {
                startMonthYear,
                endMonthYear,
            }),
            this._keywordSearchImpressionsService.getKeywordSearchImpressionsInsights(restaurantId, {
                startMonthYear,
                endMonthYear,
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            }),

            // Notoriety and discovery search keywords
            this._keywordSearchImpressionsService.getTopKeywordSearchImpressions(restaurantId, {
                startMonthYear,
                endMonthYear,
                limit: 3,
            }),
            this._keywordSearchImpressionsService.getTopKeywordSearchImpressions(restaurantId, {
                startMonthYear: previousStartMonthYear,
                endMonthYear: previousEndMonthYear,
                limit: 3,
            }),

            // Posts
            this._postsService.getRestaurantPostsPaginated(
                restaurantId,
                {
                    pageNumber: 1,
                    pageSize: 20,
                    skip: 20,
                    total: 0,
                },
                {
                    publicationStatus: [PostPublicationStatus.PUBLISHED, PostPublicationStatus.PENDING],
                    period: MalouPeriod.CUSTOM,
                    platforms: [PlatformKey.GMB],
                    startDate,
                    endDate,
                }
            ),
            this._postsService.getRestaurantPostsPaginated(
                restaurantId,
                // I don't really need it,
                {
                    pageNumber: 1,
                    pageSize: 20,
                    skip: 20,
                    total: 0,
                },
                {
                    publicationStatus: [PostPublicationStatus.PUBLISHED, PostPublicationStatus.PENDING],
                    period: MalouPeriod.CUSTOM,
                    platforms: [PlatformKey.GMB],
                    startDate: previousStartDate,
                    endDate: previousEndDate,
                }
            ),

            of({
                restaurantId,
                startDate: filters.startDate,
                endDate: filters.endDate,
                previousStartDate,
                previousEndDate,
            }),
        ]).pipe(
            map(
                ([
                    currentActionInsights,
                    previousActionInsights,
                    keywords,
                    currentKeywords,
                    previousKeywords,
                    currentSearchImpressions,
                    previousSearchImpressions,
                    topKeywordSearchImpressions,
                    previousTopKeywordSearchImpressions,
                    currentPosts,
                    previousPosts,
                    // eslint-disable-next-line @typescript-eslint/no-shadow
                    { restaurantId, startDate, endDate, previousStartDate, previousEndDate },
                ]) => {
                    const seoSectionData = this._getActionsAndImpressionsData(currentActionInsights.data, restaurantId);
                    const previousSeoSectionData = this._getActionsAndImpressionsData(previousActionInsights.data, restaurantId);

                    if (!seoSectionData) {
                        return {
                            current: null,
                            previous: null,
                        };
                    }

                    // -- keywords
                    const currentKeywordCountInTop20 = currentKeywords.keywords.filter((keyword) => {
                        const rankToCheck = keyword.rankHistory.at(-1)?.rank;
                        if (isNotNil(rankToCheck)) {
                            return rankToCheck <= 20;
                        }
                        return false;
                    }).length;
                    const previousKeywordCountInTop20 = previousKeywords.keywords.filter((keyword) => {
                        const rankToCheck = keyword.rankHistory.at(-1)?.rank;
                        if (isNotNil(rankToCheck)) {
                            return rankToCheck <= 20;
                        }
                        return false;
                    }).length;
                    seoSectionData.keywordsInTop20Count = currentKeywordCountInTop20;
                    if (previousSeoSectionData) {
                        previousSeoSectionData.keywordsInTop20Count = previousKeywordCountInTop20;
                    }

                    // -- keywords impressions

                    const keywordsImpressions = this._getKeywordsImpressions(
                        keywords,
                        startDate,
                        endDate,
                        previousStartDate,
                        previousEndDate
                    );
                    seoSectionData.keywords = keywordsImpressions.map((keyword) => ({
                        keyword: keyword.keyword,
                        impressionCount: keyword.currentImpressions,
                        evolution: keyword.impressionEvolution,
                    }));
                    if (previousSeoSectionData) {
                        previousSeoSectionData.keywords = keywordsImpressions.map((keyword) => ({
                            keyword: keyword.keyword,
                            impressionCount: keyword.previousImpressions,
                        }));
                    }

                    // -- Notoriety and discovery search
                    const totalCurrentNotorietySearchCount = currentSearchImpressions.reduce(
                        (acc, curr) => (acc ?? 0) + (curr.branding ?? 0),
                        0
                    );
                    const totalCurrentDiscoverySearchCount = currentSearchImpressions.reduce(
                        (acc, curr) => (acc ?? 0) + (curr.discovery ?? 0),
                        0
                    );
                    seoSectionData.totalNotorietySearchCount = totalCurrentNotorietySearchCount;
                    seoSectionData.totalDiscoverySearchCount = totalCurrentDiscoverySearchCount;
                    seoSectionData.top3KeywordsDiscovery = topKeywordSearchImpressions.discovery.map((keyword) => keyword.keywordSearch);
                    seoSectionData.top3KeywordsNotoriety = topKeywordSearchImpressions.branding.map((keyword) => keyword.keywordSearch);
                    seoSectionData.googlePostCount = currentPosts.data.pagination.total;
                    if (previousSeoSectionData) {
                        const previousMonthYearsToExclude = this._getPreviousMonthYearToExclude(currentSearchImpressions, {
                            startMonthYear: filters.startMonthYear,
                            endMonthYear: filters.endMonthYear,
                        });
                        const previousImpressionsDataFiltered = previousSearchImpressions.filter(
                            (impressionsData) =>
                                !previousMonthYearsToExclude.some(
                                    (monthYear) => monthYear.month === impressionsData.month && monthYear.year === impressionsData.year
                                )
                        );
                        const totalPreviousNotorietySearchCount = previousImpressionsDataFiltered.reduce(
                            (acc, curr) => (acc ?? 0) + (curr.branding ?? 0),
                            0
                        );
                        const totalPreviousDiscoverySearchCount = previousImpressionsDataFiltered.reduce(
                            (acc, curr) => (acc ?? 0) + (curr.discovery ?? 0),
                            0
                        );
                        previousSeoSectionData.totalNotorietySearchCount = totalPreviousNotorietySearchCount;
                        previousSeoSectionData.totalDiscoverySearchCount = totalPreviousDiscoverySearchCount;
                        previousSeoSectionData.top3KeywordsDiscovery = previousTopKeywordSearchImpressions.discovery.map(
                            (keyword) => keyword.keywordSearch
                        );
                        previousSeoSectionData.top3KeywordsNotoriety = previousTopKeywordSearchImpressions.branding.map(
                            (keyword) => keyword.keywordSearch
                        );
                        previousSeoSectionData.googlePostCount = previousPosts.data.pagination.total;
                    }

                    return {
                        current: seoSectionData,
                        previous: previousSeoSectionData,
                    };
                }
            ),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _getActionsAndImpressionsData(data: StoredInsightsResponseDto, restaurantId: string): SummarySectionsData['seo'] {
        const actionAndImpressionsInsights = data?.[restaurantId]?.[PlatformKey.GMB];
        if (!actionAndImpressionsInsights?.hasData) {
            return {
                actions: {
                    totalActions: null,
                    conversionRate: null,
                    totalCallCount: null,
                    totalDirectionCount: null,
                    totalWebsiteVisitCount: null,
                    totalMenuClickCount: null,
                    totalBookingClickCount: null,
                    totalOrderClickCount: null,
                },
                impressions: {
                    totalAppearanceMaps: null,
                    totalAppearanceSearch: null,
                    totalAppearance: null,
                },
                keywordsInTop20Count: null,
                totalNotorietySearchCount: null,
                totalDiscoverySearchCount: null,
                top3KeywordsNotoriety: null,
                top3KeywordsDiscovery: null,
                keywords: [],
                googlePostCount: null,
            };
        }
        const actionsData = new GmbInsightsChartData({
            data: actionAndImpressionsInsights.insights,
            startDate: data.startDate,
            endDate: data.endDate,
            aggregationTimeScale: AggregationTimeScale.BY_DAY,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        return {
            actions: {
                totalActions: actionsData.totalActions,
                conversionRate: actionsData.ratioActionsOverImpressions,
                totalCallCount: actionsData.phoneClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalDirectionCount: actionsData.drivingClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalWebsiteVisitCount: actionsData.websiteClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalMenuClickCount: actionsData.menuClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalBookingClickCount: actionsData.bookingClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalOrderClickCount: actionsData.foodOrderClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
            },
            impressions: {
                totalAppearanceMaps: actionsData.totalImpressionsMaps,
                totalAppearanceSearch: actionsData.totalImpressionsSearch,
                totalAppearance: actionsData.totalImpressions,
            },
            keywordsInTop20Count: null,
            totalNotorietySearchCount: null,
            totalDiscoverySearchCount: null,
            top3KeywordsNotoriety: null,
            top3KeywordsDiscovery: null,
            keywords: [],
            googlePostCount: null,
        };
    }

    private _getPreviousMonthYearToExclude(
        currentImpressionsData: ImpressionsInsightsData[],
        monthYearPeriod: MonthYearPeriod
    ): MonthAndYear[] {
        const monthAndYearRange: MonthAndYear[] = getRangeForMonthYearPeriod(monthYearPeriod);
        const currentMonthYearsWithoutData = monthAndYearRange.filter(
            (monthAndYear) =>
                !currentImpressionsData.some((insight) => insight.month === monthAndYear.month && insight.year === monthAndYear.year)
        );
        const monthsCount = monthAndYearRange.length;
        return currentMonthYearsWithoutData.map((monthAndYear) => getPreviousMonthYear(monthAndYear, monthsCount));
    }

    private _getKeywordsImpressions(
        keywords: Keyword[],
        startDate: Date,
        endDate: Date,
        previousStartDate: Date,
        previousEndDate: Date
    ): {
        keyword: string;
        currentImpressions: number;
        previousImpressions: number;
        impressionEvolution: number;
    }[] {
        return keywords
            .filter((keyword) => keyword.selected)
            .map((keyword) => {
                const currentImpressions = keyword.impressionsHistory.filter((impression) => {
                    const impressionDate = new Date(impression.date);
                    return impressionDate >= startDate && impressionDate <= endDate;
                });

                const previousImpressions = keyword.impressionsHistory.filter((impression) => {
                    const impressionDate = new Date(impression.date);
                    return impressionDate >= previousStartDate && impressionDate <= previousEndDate;
                });

                const currentImpressionsValue = currentImpressions.reduce((acc, curr) => acc + curr.value, 0);
                const previousImpressionsValue = previousImpressions.reduce((acc, curr) => acc + curr.value, 0);
                let impressionEvolution = currentImpressionsValue;
                if (currentImpressionsValue) {
                    impressionEvolution = currentImpressionsValue - previousImpressionsValue;
                }

                return {
                    keyword: keyword.text,
                    currentImpressions: currentImpressionsValue,
                    previousImpressions: previousImpressionsValue,
                    impressionEvolution,
                };
            })
            .filter(isNotNil);
    }
}
