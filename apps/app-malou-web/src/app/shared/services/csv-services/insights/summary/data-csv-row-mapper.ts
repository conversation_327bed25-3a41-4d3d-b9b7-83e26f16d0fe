import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { isNil, round } from 'lodash';

import { TimeInMilliseconds, TimeInMinutes } from '@malou-io/package-utils';

import { StarValue } from ':core/constants';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';
import {
    SummarySection,
    SummarySectionsData,
    SummarySectionsTranslationKeys,
} from ':shared/services/csv-services/insights/summary/summary.interface';

const DEFAULT_CSV_EMPTY_VALUE = 'N/A';
const DEFAULT_EVOLUTION_VALUE = '-';
@Injectable({ providedIn: 'root' })
export class DataToCsvRowMapperService {
    constructor(
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _translate: TranslateService
    ) {}

    mapSeoSectionDataToCsvRows(currentData: SummarySectionsData['seo'], previousData: SummarySectionsData['seo']): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const impressionRows = Object.entries(currentData.impressions).map(([key, value]) => {
            if (key === 'totalAppearance') {
                return [
                    this._getRowNameFromKey(`impressions.${key}`, SummarySection.SEO),
                    this._formatNumber(value),
                    this._formatNumber(previousData?.impressions[key]),
                    this._formatNumber(this._computeEvolution(value, previousData?.impressions[key], { isPercentageOfPrevious: true }), {
                        isPercentage: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            return [
                this._getRowNameFromKey(`impressions.${key}`, SummarySection.SEO),
                this._formatNumber(value),
                this._formatNumber(previousData?.impressions[key]),
                this._formatNumber(this._computeEvolution(value, previousData?.impressions[key])),
            ];
        });

        const actionRows = Object.entries(currentData.actions).map(([key, value]) => {
            if (key === 'totalActions') {
                return [
                    this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                    this._formatNumber(value),
                    this._formatNumber(previousData?.actions[key]),
                    this._formatNumber(this._computeEvolution(value, previousData?.actions[key], { isPercentageOfPrevious: true }), {
                        isPercentage: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            if (key === 'conversionRate') {
                return [
                    this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                    this._formatNumber(value, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                    this._formatNumber(previousData?.actions[key], { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                    this._formatNumber(this._computeEvolution(value, previousData?.actions[key]), {
                        isPercentageEvolution: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            return [
                this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                this._formatNumber(value),
                this._formatNumber(previousData?.actions[key]),
                this._formatNumber(this._computeEvolution(value, previousData?.actions[key])),
            ];
        });

        const otherRows = [
            [
                this._getRowNameFromKey('keywordsInTop20Count', SummarySection.SEO),
                this._formatNumber(currentData.keywordsInTop20Count),
                this._formatNumber(previousData?.keywordsInTop20Count),
                this._formatNumber(this._computeEvolution(currentData.keywordsInTop20Count, previousData?.keywordsInTop20Count)),
            ],
            [
                this._getRowNameFromKey('totalNotorietySearchCount', SummarySection.SEO),
                this._formatNumber(currentData.totalNotorietySearchCount),
                this._formatNumber(previousData?.totalNotorietySearchCount),
                this._formatNumber(this._computeEvolution(currentData.totalNotorietySearchCount, previousData?.totalNotorietySearchCount)),
            ],
            [
                this._getRowNameFromKey('totalDiscoverySearchCount', SummarySection.SEO),
                this._formatNumber(currentData.totalDiscoverySearchCount),
                this._formatNumber(previousData?.totalDiscoverySearchCount),
                this._formatNumber(this._computeEvolution(currentData.totalDiscoverySearchCount, previousData?.totalDiscoverySearchCount)),
            ],
        ];

        const top3KeywordsDiscoveryRows =
            currentData.top3KeywordsDiscovery?.map((keyword, index) => [
                this._getRowNameFromKey('discovery', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.top3KeywordsDiscovery?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const top3KeywordsNotorietyRows =
            currentData.top3KeywordsNotoriety?.map((keyword, index) => [
                this._getRowNameFromKey('notoriety', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.top3KeywordsNotoriety?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const keywordsRows =
            currentData.keywords.map((keyword, index) => [
                this._getRowNameFromKey('keywordImpression', 'seo', { index: `${index + 1}` }),
                keyword.keyword + ' - ' + this._formatNumber(keyword.impressionCount),
                previousData?.keywords[index]?.keyword + ' - ' + this._formatNumber(previousData?.keywords[index]?.impressionCount),
                this._formatNumber(keyword.evolution),
            ]) ?? [];

        const googlePostCountRow = [
            [
                this._getRowNameFromKey('googlePostCount', 'seo'),
                this._formatNumber(currentData.googlePostCount),
                this._formatNumber(previousData?.googlePostCount),
                this._formatNumber(this._computeEvolution(currentData.googlePostCount, previousData?.googlePostCount)),
            ],
        ];
        return [
            [this._getRowNameFromKey('seo', SummarySection.SEO), '-', '-', '-'],
            ...impressionRows,
            ...actionRows,
            ...otherRows,
            ...top3KeywordsNotorietyRows,
            ...top3KeywordsDiscoveryRows,
            ...keywordsRows,
            ...googlePostCountRow,
        ];
    }

    mapEReputationSectionDataToCsvRows(
        currentData: SummarySectionsData['eReputation'],
        previousData: SummarySectionsData['eReputation']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const notesRows = currentData.notes
            .filter((platformNote) => platformNote.note !== 1)
            .map(({ platform, note }) => [
                this._getRowNameFromKey('rating', SummarySection.E_REPUTATION, {
                    platform: this._enumTranslatePipe.transform(platform, 'platform_key'),
                }),
                this._formatNumber(note, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.notes.find((n) => n.platform === platform)?.note, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(note, previousData?.notes.find((n) => n.platform === platform)?.note), {
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ]);

        const reviewsRows = [
            [
                this._getRowNameFromKey('reviews.totalCount', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviews.totalCount),
                this._formatNumber(previousData?.reviews.totalCount),
                this._formatNumber(this._computeEvolution(currentData.reviews.totalCount, previousData?.reviews.totalCount)),
            ],
            [
                this._getRowNameFromKey('reviews.averageRating', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviews.averageRating, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.reviews.averageRating, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(currentData.reviews.averageRating, previousData?.reviews.averageRating), {
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ],
        ];

        const reviewsRatingsRows = Object.entries(currentData.reviewsRatings)
            .filter(([key]) => key !== StarValue.UNKNOWN.toString())
            .map(([key, value]) => [
                this._getRowNameFromKey(key === StarValue.ONE.toString() ? 'star.one' : 'star.other', SummarySection.E_REPUTATION, {
                    starValue: key,
                }),
                this._formatNumber(value, { isPercentage: true, shouldRound: true, decimalPlaces: 0 }),
                this._formatNumber(previousData?.reviewsRatings[key], { isPercentage: true, shouldRound: true, decimalPlaces: 0 }),
                this._formatNumber(this._computeEvolution(value, previousData?.reviewsRatings[key]), {
                    isPercentageEvolution: true,
                    shouldRound: true,
                    decimalPlaces: 0,
                }),
            ]);

        const reviewsResponseRows = [
            [
                this._getRowNameFromKey('reviewsResponse.rate', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviewsResponse.rate, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.reviewsResponse.rate, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(currentData.reviewsResponse.rate, previousData?.reviewsResponse.rate), {
                    isPercentageEvolution: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ],
            [
                this._getRowNameFromKey('reviewsResponse.averageTime', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviewsResponse.averageTime, { isTime: true }),
                this._formatNumber(previousData?.reviewsResponse.averageTime, { isTime: true }),
                this._formatNumber(
                    this._computeEvolution(currentData.reviewsResponse.averageTime, previousData?.reviewsResponse.averageTime),
                    { isTime: true }
                ),
            ],
        ];

        const sentimentsPercentageRows = [
            [
                this._getRowNameFromKey('sentimentsPercentage.positive', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.sentimentsPercentage.positive, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.sentimentsPercentage.positive, {
                    isPercentage: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
                this._formatNumber(
                    this._computeEvolution(currentData.sentimentsPercentage.positive, previousData?.sentimentsPercentage.positive),
                    { isPercentageEvolution: true, shouldRound: true, decimalPlaces: 2 }
                ),
            ],
            [
                this._getRowNameFromKey('sentimentsPercentage.negative', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.sentimentsPercentage.negative, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.sentimentsPercentage.negative, {
                    isPercentage: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
                this._formatNumber(
                    this._computeEvolution(currentData.sentimentsPercentage.negative, previousData?.sentimentsPercentage.negative),
                    { isPercentageEvolution: true, shouldRound: true, decimalPlaces: 2 }
                ),
            ],
        ];

        const top3SubjectsRows =
            currentData.top3Subjects?.map((subject, index) => [
                this._getRowNameFromKey('subject.top', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.top3Subjects?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3SubjectsRows =
            currentData.flop3Subjects?.map((subject, index) => [
                this._getRowNameFromKey('subject.flop', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.flop3Subjects?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        return [
            [this._getRowNameFromKey('e_reputation', SummarySection.E_REPUTATION), '-', '-', '-'],
            ...notesRows,
            ...reviewsRows,
            ...reviewsRatingsRows,
            ...reviewsResponseRows,
            ...sentimentsPercentageRows,
            ...top3SubjectsRows,
            ...flop3SubjectsRows,
        ];
    }

    mapBoosterSectionDataToCsvRows(
        currentData: SummarySectionsData['booster'],
        previousData: SummarySectionsData['booster']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }
        const { totalTotemScanCount, totemCountEvolution, gainedReviewCountPerRating, topTotems, ...other } = currentData;
        const {
            totalTotemScanCount: previousTotalTotemScanCount,
            gainedReviewCountPerRating: previousGainedReviewCountPerRating,
            topTotems: previousTopTotems,
            ...previousOther
        } = previousData ?? {};

        const totalTotemScanCountRow = [
            this._getRowNameFromKey('totalTotemScanCount', SummarySection.BOOSTER),
            this._formatNumber(totalTotemScanCount),
            this._formatNumber(previousTotalTotemScanCount),
            this._formatNumber(totemCountEvolution),
        ];

        const notesRows = Object.entries(gainedReviewCountPerRating)
            .filter(([key]) => key !== StarValue.UNKNOWN.toString())
            .map(([key, value]) => [
                this._getRowNameFromKey(key === StarValue.ONE.toString() ? 'star.one' : 'star.other', SummarySection.BOOSTER, {
                    starValue: key,
                }),
                this._formatNumber(value),
                this._formatNumber(previousGainedReviewCountPerRating?.[key]),
                this._formatNumber(this._computeEvolution(value, previousGainedReviewCountPerRating?.[key])),
            ]);

        const topTotemsRows =
            topTotems?.map((totem, index) => [
                this._getRowNameFromKey('totem', 'booster', { index: `${index + 1}` }),
                totem,
                previousTopTotems?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const otherRows = Object.entries(other).map(([key, value]) => [
            this._getRowNameFromKey(key, SummarySection.BOOSTER),
            this._formatNumber(value),
            this._formatNumber(previousOther[key]),
            this._formatNumber(this._computeEvolution(value, previousOther[key])),
        ]);

        return [
            [this._getRowNameFromKey('booster', SummarySection.BOOSTER), '-', '-', '-'],
            totalTotemScanCountRow,
            ...otherRows,
            ...notesRows,
            ...topTotemsRows,
        ];
    }

    mapSocialMediaSectionDataToCsvRows(
        currentData: SummarySectionsData['socialMedia'],
        previousData: SummarySectionsData['socialMedia']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const rows = [
            [
                this._getRowNameFromKey('followerCount', SummarySection.SOCIAL_MEDIA, { platform: 'Instagram' }),
                this._formatNumber(currentData.followerCount),
                this._formatNumber(previousData?.followerCount),
                this._formatNumber(this._computeEvolution(currentData.followerCount, previousData?.followerCount)),
            ],
            [
                this._getRowNameFromKey('engagementRate', SummarySection.SOCIAL_MEDIA, { platform: 'Instagram' }),
                this._formatNumber(currentData.engagementRate, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.engagementRate, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(currentData.engagementRate, previousData?.engagementRate), {
                    isPercentageEvolution: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ],
            [
                this._getRowNameFromKey('impressionCount', SummarySection.SOCIAL_MEDIA, { platform: 'Instagram' }),
                this._formatNumber(currentData.impressionCount),
                this._formatNumber(previousData?.impressionCount),
                this._formatNumber(this._computeEvolution(currentData.impressionCount, previousData?.impressionCount)),
            ],
            [
                this._getRowNameFromKey('postCount', SummarySection.SOCIAL_MEDIA, { platform: 'Instagram' }),
                this._formatNumber(currentData.postCount),
                this._formatNumber(previousData?.postCount),
                this._formatNumber(this._computeEvolution(currentData.postCount, previousData?.postCount)),
            ],
        ];

        return [[this._getRowNameFromKey('socialMedia', SummarySection.SOCIAL_MEDIA), '-', '-', '-'], ...rows];
    }

    mapRoiSectionDataToCsvRows(currentData: SummarySectionsData['roi']): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const rows = [
            [
                this._getRowNameFromKey('additionalClientCount', SummarySection.ROI),
                this._formatNumber(currentData.additionalClientCount.min).concat(
                    ' - ',
                    this._formatNumber(currentData.additionalClientCount.max)
                ),
                DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ],
            [
                this._getRowNameFromKey('additionalSalesRevenue', SummarySection.ROI),
                `${this._formatNumber(currentData.additionalSalesRevenue.min, { shouldRoundToTens: true })} - ${this._formatNumber(
                    currentData.additionalSalesRevenue.max,
                    { shouldRoundToTens: true }
                )}`,
                DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ],
            [
                this._getRowNameFromKey('savedTime', SummarySection.ROI),
                this._formatNumber(currentData.savedTime, { isTime: true }),
                DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ],
            [
                this._getRowNameFromKey('performanceScore', SummarySection.ROI),
                this._formatNumber(currentData.performanceScore, { shouldRound: true, decimalPlaces: 0 }),
                DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ],
        ];

        return [[this._getRowNameFromKey('roi', SummarySection.ROI), '-', '-', '-'], ...rows];
    }

    private _getRowNameFromKey(
        translationKey: string,
        section: SummarySectionsTranslationKeys,
        interpolateParams?: Record<string, string>
    ): string {
        return this._enumTranslatePipe.transform(
            section,
            'csv_insights_field_names.summary',
            translationKey.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase(),
            interpolateParams
        );
    }

    private _computeEvolution(
        currentValue: number | null,
        previousValue: number | null | undefined,
        options?: { isPercentageOfPrevious?: boolean }
    ): number | string | null {
        if (currentValue === null || isNil(previousValue) || isNaN(currentValue) || isNaN(previousValue)) {
            return DEFAULT_EVOLUTION_VALUE;
        }
        if (options?.isPercentageOfPrevious) {
            if (isNil(previousValue) || previousValue === 0) {
                return DEFAULT_EVOLUTION_VALUE;
            }
            return ((currentValue - previousValue) / previousValue) * 100;
        }
        return currentValue - previousValue;
    }

    private _formatNumber(
        value: number | string | null | undefined,
        options?: {
            isPercentageEvolution?: boolean;
            isPercentage?: boolean;
            shouldRound?: boolean;
            shouldRoundToTens?: boolean;
            decimalPlaces?: number;
            isTime?: boolean;
        }
    ): string {
        if (typeof value === 'string') {
            return value;
        }
        if (isNil(value) || isNaN(value)) {
            return DEFAULT_CSV_EMPTY_VALUE;
        }
        let formattedNumber = value.toString();
        if (options?.shouldRoundToTens) {
            return value >= 1000 ? `${round(value / 1000)}k €` : `${value}€`;
        }
        if (options?.isTime) {
            return this._formatTime(value);
        }
        if (options?.shouldRound) {
            formattedNumber = `${round(value, options.decimalPlaces ?? 0)}`;
        }
        if (options?.isPercentageEvolution) {
            formattedNumber = `${formattedNumber}pts`;
        }
        if (options?.isPercentage) {
            formattedNumber = `${formattedNumber}%`;
        }
        return formattedNumber;
    }

    private _formatTime(value: number): string {
        const hours = Math.floor(value / TimeInMilliseconds.HOUR);
        const minutes = Math.floor((value / TimeInMilliseconds.MINUTE) % TimeInMinutes.HOUR);

        if (hours < 1 && minutes > 0) {
            return minutes + this._translate.instant('common.minutes_short');
        }

        if (hours === 0 && minutes === 0) {
            return '0' + this._translate.instant('common.hours_short');
        }

        const min = minutes ? minutes + this._translate.instant('common.minutes_short') : '';
        return hours + this._translate.instant('common.hours_short') + ' ' + min;
    }
}
