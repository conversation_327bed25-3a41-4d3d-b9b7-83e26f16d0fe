from typing import Type
from anthropic import Anthropic
import time
import json
import os
from typing import Optional, List, Dict
from pydantic import BaseModel

from ...ai_provider_abstract import (
    AI_MAX_RETRIES,
    AbstractAiProvider,
    AiResponseMetadata,
)
import instructor


class AiConfig(BaseModel):
    frequencyPenalty: Optional[float] = 0
    temperature: Optional[float] = 1.0
    maxTokens: Optional[float] = 1000
    timeout: Optional[float] = 55
    model: Optional[str] = "gpt-4o-mini"
    topP: Optional[float] = 1.0


class AiResponseMetadata(BaseModel):
    message: List[Dict[str, str]]
    completion_text: str
    completion_tokens: int
    prompt_tokens: int
    request_response_time: int
    ai_model_config: Optional[AiConfig] = None
    number_of_retries: Optional[int] = 0


class AiResponse(BaseModel):
    response: BaseModel
    metadata: AiResponseMetadata


try:
    client_anthropic = instructor.from_anthropic(
        Anthropic(
            api_key=os.environ.get(
                "ANTHROPIC_API_KEY",
                "************************************************************************************************************",
            )
        )
    )
except:
    client_anthropic = None

AI_MODEL = "claude-3-5-sonnet-20240620"


class AnthropicAdapter(AbstractAiProvider):

    def call(
        self, prompt: str, configuration: AiConfig, response_model: Type[BaseModel]
    ) -> AiResponse:

        start_time = time.time()
        response = client_anthropic.messages.create(
            model=configuration.model,
            temperature=configuration.temperature,
            max_tokens=configuration.max_tokens,
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            response_model=response_model,
        )
        end_time = time.time()
        temp = response.__dict__["_raw_response"].model_dump_json()

        # convert str to json temp
        temp = json.loads(temp)
        metadata = AiResponseMetadata(
            message=[{"user": prompt}],
            completion_text=str(response),
            completion_tokens=temp["usage"]["output_tokens"],
            prompt_tokens=temp["usage"]["input_tokens"],
            request_response_time=int((end_time - start_time) * 1000),
        ).model_dump()

        return AiResponse(response=response, metadata=metadata)

    def callWithContext(
        self,
        prompt: str,
        context: str,
        configuration: AiConfig,
        response_model: Type[BaseModel],
    ) -> AiResponse:

        start_time = time.time()
        response = client_anthropic.messages.create(
            model=configuration.model,
            temperature=configuration.temperature,
            max_tokens=configuration.maxTokens,
            system=context,
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            response_model=response_model,
        )
        end_time = time.time()
        temp = json.loads(response.__dict__["_raw_response"].model_dump_json())
        metadata = AiResponseMetadata(
            message=[{"system": context}, {"user": prompt}],
            completion_text=str(response),
            completion_tokens=temp["usage"]["output_tokens"],
            prompt_tokens=temp["usage"]["input_tokens"],
            request_response_time=int((end_time - start_time) * 1000),
        ).model_dump()
        return AiResponse(response=response, metadata=metadata)
