from typing import Type
from openai import OpenAI
import time
import os
from typing import Optional, List, Dict
from pydantic import BaseModel
import instructor
from ...ai_provider_abstract import (
    AI_MAX_RETRIES,
    AbstractAiProvider,
    AiResponseMetadata,
)

try:
    client = instructor.from_openai(
        OpenAI(
            api_key=os.environ.get(
                "OPENAI_API_KEY",
            )
        )
    )
    AI_MODEL = "gpt-4o-mini"
    client.default_model = AI_MODEL

except:
    client = None


class AiConfig(BaseModel):
    frequencyPenalty: Optional[float] = 0
    temperature: Optional[float] = 1.0
    maxTokens: Optional[float] = 1000
    timeout: Optional[float] = 55
    model: Optional[str] = "gpt-4o-mini"
    topP: Optional[float] = 1.0


class AiResponseMetadata(BaseModel):
    message: List[Dict[str, str]]
    completion_text: str
    completion_tokens: int
    prompt_tokens: int
    request_response_time: int
    ai_model_config: Optional[AiConfig] = None
    number_of_retries: Optional[int] = 0


class AiResponse(BaseModel):
    response: BaseModel
    metadata: AiResponseMetadata


class OpenAiAdapter(AbstractAiProvider):

    def call(
        self, prompt: str, configuration: AiConfig, response_model: Type[BaseModel]
    ) -> AiResponse:
        start_time = time.time()
        chat_completion = client.create_with_completion(
            messages=[
                {"role": "user", "content": prompt},
            ],
            response_model=response_model,
            max_retries=AI_MAX_RETRIES,
            model=AI_MODEL,
        )
        end_time = time.time()
        metadata = AiResponseMetadata(
            message=[{"user": prompt}],
            completion_text=str(chat_completion[0]),
            completion_tokens=chat_completion[1].usage.completion_tokens,
            prompt_tokens=chat_completion[1].usage.prompt_tokens,
            request_response_time=int((end_time - start_time) * 1000),
        ).model_dump()
        return AiResponse(
            response=chat_completion[0], metadata=metadata  # type: ignore
        )

    def callWithContext(
        self,
        prompt: str,
        context: str,
        configuration: AiConfig,
        response_model: Type[BaseModel],
    ) -> AiResponse:
        start_time = time.time()

        chat_completion = client.create_with_completion(
            messages=[
                {"role": "system", "content": context},
                {"role": "user", "content": prompt},
            ],
            response_model=response_model,
            temperature=configuration.temperature,
            max_tokens=configuration.maxTokens,
            frequency_penalty=configuration.frequencyPenalty,
            top_p=configuration.topP,
            timeout=configuration.timeout,
            model=configuration.model,
        )

        end_time = time.time()
        metadata = AiResponseMetadata(
            message=[{"system": context}, {"user": prompt}],
            completion_text=str(chat_completion[0]),
            completion_tokens=chat_completion[1].usage.completion_tokens,
            prompt_tokens=chat_completion[1].usage.prompt_tokens,
            request_response_time=int((end_time - start_time) * 1000),
            ai_model_config=AiConfig(
                frequencyPenalty=configuration.frequencyPenalty,
                topP=configuration.topP,
                temperature=configuration.temperature,
                maxTokens=configuration.maxTokens,
                timeout=configuration.timeout,
                model=configuration.model,
            ),
        ).model_dump()

        return AiResponse(response=chat_completion[0], metadata=metadata)
