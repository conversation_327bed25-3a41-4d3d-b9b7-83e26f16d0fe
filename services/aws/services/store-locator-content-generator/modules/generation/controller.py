from modules.generation.handler import TextGenerator
from modules.generation.model import (
    TextGenerationPayload,
    TextGeneration,
)


class TextGeneratorController:

    def __init__(
        self,
        textGenerator: TextGenerator = TextGenerator(),
    ):
        self._generate = textGenerator

    def generate(self, event: TextGenerationPayload) -> TextGeneration:
        return self._generate.execute(payload=event)
