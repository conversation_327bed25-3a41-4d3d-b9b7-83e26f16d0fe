import { DateTime, Interval } from 'luxon';

import { MonthYearPeriod } from '../../date';
import { MalouComparisonPeriod } from './interface';

interface GetDateRangeFromMalouComparisonPeriodInput {
    restaurantStartDate?: Date;
    dateFilters: {
        startDate: Date;
        endDate: Date;
    };
    comparisonPeriod: MalouComparisonPeriod;
}

interface GetDateRangeFromMalouComparisonPeriodOutput {
    startDate: Date | null;
    endDate: Date | null;
}

export const getDateRangeFromMalouComparisonPeriod = ({
    restaurantStartDate,
    dateFilters,
    comparisonPeriod,
}: GetDateRangeFromMalouComparisonPeriodInput): GetDateRangeFromMalouComparisonPeriodOutput => {
    const { offset } = DateTime.local();
    const startDateLuxon = DateTime.fromJSDate(dateFilters.startDate).plus({ minutes: offset }).setZone('utc');
    const endDateLuxon = DateTime.fromJSDate(dateFilters.endDate).plus({ minutes: offset }).setZone('utc');
    const duration = endDateLuxon.diff(startDateLuxon).as('milliseconds');

    if (!restaurantStartDate || comparisonPeriod === MalouComparisonPeriod.PREVIOUS_PERIOD) {
        const startDateForPreviousPeriod = startDateLuxon.minus({ days: 1 });
        return {
            startDate: startDateForPreviousPeriod.minus(duration).toJSDate(),
            endDate: startDateForPreviousPeriod.toJSDate(),
        };
    }
    const restaurantStartDateLuxon = DateTime.fromJSDate(restaurantStartDate).setZone('utc');
    switch (comparisonPeriod) {
        case MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR: {
            const restaurantStartMonth = restaurantStartDateLuxon.month;
            let comparisonYear = restaurantStartDateLuxon.year;
            if (startDateLuxon.month < restaurantStartMonth) {
                comparisonYear += 1;
            }
            const firstYearStartDate = DateTime.fromObject({
                year: comparisonYear,
                month: startDateLuxon.month,
                day: startDateLuxon.day,
                hour: 0,
                minute: 0,
                second: 0,
            });

            if (firstYearStartDate.valueOf() < restaurantStartDateLuxon.valueOf()) {
                return {
                    startDate: null,
                    endDate: null,
                };
            }
            const firstYearEndDate = firstYearStartDate.plus(duration);

            return {
                startDate: firstYearStartDate.toJSDate(),
                endDate: firstYearEndDate.toJSDate(),
            };
        }
        case MalouComparisonPeriod.SINCE_START: {
            return {
                startDate: restaurantStartDateLuxon.toJSDate(),
                endDate: restaurantStartDateLuxon.plus(duration).toJSDate(),
            };
        }
        default:
            throw new Error('Invalid comparison period');
            break;
    }
};

export const getMonthYearPreviousPeriod = ({ startMonthYear, endMonthYear }: MonthYearPeriod): MonthYearPeriod => {
    const monthsCount = Interval.fromDateTimes(
        DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }),
        DateTime.fromObject({ year: endMonthYear.year, month: endMonthYear.month })
    ).splitBy({ months: 1 }).length;

    const previousStart = DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }).minus({
        months: monthsCount + 1,
    });
    const previousEnd = DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }).minus({ months: 1 });
    return {
        startMonthYear: { month: previousStart.month, year: previousStart.year },
        endMonthYear: { month: previousEnd.month, year: previousEnd.year },
    };
};
