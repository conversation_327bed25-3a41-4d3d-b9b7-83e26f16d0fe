import { DateTime, Settings } from 'luxon';

import { MalouComparisonPeriod } from './interface';
import { getDateRangeFromMalouComparisonPeriod, getMonthYearPreviousPeriod } from './utils';

describe('getDateRangeFromMalouComparisonPeriod', () => {
    const offset = DateTime.local().zoneName;
    beforeAll(() => {
        Settings.defaultZone = 'utc';
    });

    afterAll(() => {
        Settings.defaultZone = offset;
    });

    const restaurantStartDate = new Date('2020-01-01');
    const dateFilters = {
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-01-15'),
    };

    it('should return the last period date range', () => {
        const result = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        expect(result.startDate).toEqual(new Date('2024-12-17'));
        expect(result.endDate).toEqual(new Date('2024-12-31'));
    });

    it('should return the same period first year date range', () => {
        const result = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });

        expect(result.startDate).toEqual(new Date('2020-01-01'));
        expect(result.endDate).toEqual(new Date('2020-01-15'));
    });

    it('should return new year dates if the first year start date is before the restaurant start date', () => {
        const result = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate: new Date('2020-02-01'),
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        expect(result.startDate).toEqual(new Date('2021-01-01'));
        expect(result.endDate).toEqual(new Date('2021-01-15'));
    });

    it('should return the since start date range', () => {
        const result = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });
        expect(result.startDate).toEqual(new Date('2020-01-01'));
        expect(result.endDate).toEqual(new Date('2020-01-15'));
    });

    it('should throw an error for an invalid comparison period', () => {
        expect(() => {
            getDateRangeFromMalouComparisonPeriod({
                restaurantStartDate,
                dateFilters,
                comparisonPeriod: 'INVALID_PERIOD' as MalouComparisonPeriod,
            });
        }).toThrow('Invalid comparison period');
    });
});

describe('getDateRangeFromMalouComparisonPeriod scenarios', () => {
    const offset = DateTime.local().zoneName;
    beforeAll(() => {
        Settings.defaultZone = 'utc';
    });

    afterAll(() => {
        Settings.defaultZone = offset;
    });

    it('should respect scenario where restaurant startDate: 2023/01/01 and selectedDate: 2025/01/15 - 2025/01/30', () => {
        const restaurantStartDate = new Date('2023-01-01');
        const dateFilters = {
            startDate: new Date('2025-01-15'),
            endDate: new Date('2025-01-30'),
        };

        const lastPeriodResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        const samePeriodFirstYearResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        const sinceStartResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });

        expect(lastPeriodResult).toEqual({ startDate: new Date('2024-12-30'), endDate: new Date('2025-01-14') });
        expect(samePeriodFirstYearResult).toEqual({ startDate: new Date('2023-01-15'), endDate: new Date('2023-01-30') });
        expect(sinceStartResult).toEqual({ startDate: new Date('2023-01-01'), endDate: new Date('2023-01-16') });
    });

    it('should respect scenario where restaurant startDate: 2023/06/01 and selectedDate: 2025/01/01 - 2025/01/15', () => {
        const restaurantStartDate = new Date('2023-06-01');
        const dateFilters = {
            startDate: new Date('2025-01-01'),
            endDate: new Date('2025-01-15'),
        };

        const lastPeriodResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        const samePeriodFirstYearResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        const sinceStartResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });

        expect(lastPeriodResult).toEqual({ startDate: new Date('2024-12-17'), endDate: new Date('2024-12-31') });
        expect(samePeriodFirstYearResult).toEqual({ startDate: new Date('2024-01-01'), endDate: new Date('2024-01-15') });
        expect(sinceStartResult).toEqual({ startDate: new Date('2023-06-01'), endDate: new Date('2023-06-15') });
    });

    it('should respect scenario where restaurant startDate: 2023/06/10 and selectedDate: 2025/01/01 - 2025/01/15', () => {
        const restaurantStartDate = new Date('2023-06-10');
        const dateFilters = {
            startDate: new Date('2025-01-01'),
            endDate: new Date('2025-01-15'),
        };

        const lastPeriodResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        const samePeriodFirstYearResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        const sinceStartResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });

        expect(lastPeriodResult).toEqual({ startDate: new Date('2024-12-17'), endDate: new Date('2024-12-31') });
        expect(samePeriodFirstYearResult).toEqual({ startDate: new Date('2024-01-01'), endDate: new Date('2024-01-15') });
        expect(sinceStartResult).toEqual({ startDate: new Date('2023-06-10'), endDate: new Date('2023-06-24') });
    });

    it('should respect scenario where restaurant startDate: 2024/01/01 and selectedDate: 2025/01/01 - 2025/01/15', () => {
        const restaurantStartDate = new Date('2024-01-01');
        const dateFilters = {
            startDate: new Date('2025-01-01'),
            endDate: new Date('2025-01-15'),
        };

        const lastPeriodResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        const samePeriodFirstYearResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        const sinceStartResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });

        expect(lastPeriodResult).toEqual({ startDate: new Date('2024-12-17'), endDate: new Date('2024-12-31') });
        expect(samePeriodFirstYearResult).toEqual({ startDate: new Date('2024-01-01'), endDate: new Date('2024-01-15') });
        expect(sinceStartResult).toEqual({ startDate: new Date('2024-01-01'), endDate: new Date('2024-01-15') });
    });

    it('should respect scenario where restaurant startDate: 2024/01/10 and selectedDate: 2025/01/01 - 2025/01/15', () => {
        const restaurantStartDate = new Date('2024-01-10');
        const dateFilters = {
            startDate: new Date('2025-01-01'),
            endDate: new Date('2025-01-15'),
        };

        const lastPeriodResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
        const samePeriodFirstYearResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR,
        });
        const sinceStartResult = getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate,
            dateFilters,
            comparisonPeriod: MalouComparisonPeriod.SINCE_START,
        });

        expect(lastPeriodResult).toEqual({ startDate: new Date('2024-12-17'), endDate: new Date('2024-12-31') });
        expect(samePeriodFirstYearResult).toEqual({ startDate: null, endDate: null });
        expect(sinceStartResult).toEqual({ startDate: new Date('2024-01-10'), endDate: new Date('2024-01-24') });
    });
});

describe('getMonthYearPreviousPeriod', () => {
    it('should return month-year previous period for 1 month period', () => {
        const currentPeriod = {
            startMonthYear: { month: 1, year: 2025 },
            endMonthYear: { month: 1, year: 2025 },
        };

        const expectedResult = {
            startMonthYear: { month: 12, year: 2024 },
            endMonthYear: { month: 12, year: 2024 },
        };

        const result = getMonthYearPreviousPeriod(currentPeriod);

        expect(result).toEqual(expectedResult);
    });

    it('should return month-year previous period for 2 months period', () => {
        const currentPeriod = {
            startMonthYear: { month: 1, year: 2025 },
            endMonthYear: { month: 2, year: 2025 },
        };

        const expectedResult = {
            startMonthYear: { month: 11, year: 2024 },
            endMonthYear: { month: 12, year: 2024 },
        };

        const result = getMonthYearPreviousPeriod(currentPeriod);

        expect(result).toEqual(expectedResult);
    });

    it('should return month-year previous period for multiple months period', () => {
        const currentPeriod = {
            startMonthYear: { month: 1, year: 2025 },
            endMonthYear: { month: 8, year: 2025 },
        };

        const expectedResult = {
            startMonthYear: { month: 5, year: 2024 },
            endMonthYear: { month: 12, year: 2024 },
        };

        const result = getMonthYearPreviousPeriod(currentPeriod);

        expect(result).toEqual(expectedResult);
    });
});
