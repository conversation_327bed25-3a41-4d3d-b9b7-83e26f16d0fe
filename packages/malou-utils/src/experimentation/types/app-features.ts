/**
 * GrowthBook Features TypeScript Typings
 *
 * NOTE: This file is auto generated by the GrowthBook CLI (https://docs.growthbook.io/tools/cli).
 * See https://docs.growthbook.io/tools/cli#generating-types for more information.
 *
 * DO NOT EDIT this file manually.
 */
export type AppFeatures = {
    'release-yext-update': boolean;
    'release-reels-editor': boolean;
    'release-roi': boolean;
    'kill-switch-fetch-keywords-volume': boolean;
    'release-post-hashtags-and-ai': boolean;
    'release-review-reply-optimistic-update': boolean;
    'release-new-platforms-connection-flow': boolean;
    'release-platform-connexion-redesign': boolean;
    'kill-switch-mobile-force-update': boolean;
    'experiment-simpler-combined-reviews': boolean;
    'experiment-checklist': boolean;
    'release-keywords-breakdown-translations': boolean;
    'experiment-reviews-db-query': boolean;
    'release-custom-ai-settings': boolean;
    'release-opentable-platform': boolean;
    'release-abc-platform': boolean;
    'experiment-reviews-sort-options-date-only': boolean;
    'release-post-media-upload-performance': boolean;
    'release-platforms-updates': boolean;
    'release-notification-center': boolean;
    'release-negative-reviews-email-notifications': boolean;
    'release-special-hour-email-notifications': boolean;
    'release-email-notifications': boolean;
    'fast-keyword-stats-v3': boolean;
    'release-post-suggestion-notifications': boolean;
    'kill-switch-cache-keywords-score': boolean;
    'release-post-suggestion-email-notifications': boolean;
    'release-post-error-notification': boolean;
    'release-roi-activated-email-notification': boolean;
    'release-comments-notification': boolean;
    'release-mentions-notification': boolean;
    'release-new-message-notification': boolean;
    'experiment-referral-btn-text': boolean;
    'gbdemo-checkout-layout': string;
    'release-notifications-email-summary': boolean;
    'release-post-lambda-advanced-settings': boolean;
    'release-ai-media-description': boolean;
    'release-ubereats-promotion-offer': boolean;
    'kill-switch-memory-manager': boolean;
    'release-tiktok-platform': boolean;
    'release-new-social-posts-and-medias': boolean;
    'experiment-autocomplete': number;
    'big-duplication-modal-post-preview': boolean;
    'release-platform-disconnected-notification': boolean;
    'import-media-v2': boolean;
    'release-aggregated-social-media-performance-improvements': boolean;
    'experimentation-chat-bubble-click': boolean;
    'kill-switch-christmas': boolean;
    'release-review-revamp': boolean;
    'release-booster-performance-improvements': boolean;
    'release-info-update-error-notification': boolean;
    'release-gmb-insights-charts-v2': boolean;
    'release-aggregated--gmb-insights-charts-v2': boolean;
    'release-review-analysis-insights-v2': boolean;
    'release-big-duplication-modal-social-post': boolean;
    'release-new-semantic-analysis': boolean;
    'release-new-semantic-analysis-v2': boolean;
    'release-feed-4-5': boolean;
    'release-review-insights-v2': boolean;
    'release-simple-social-media-performance-improvements': boolean;
    'release-statistics-new-calendar': boolean;
    'release-statistics-new-calendar-since-start-option': boolean;
    'release-keywords-insights-v2': boolean;
    'release-new-post-publication': boolean;
    'release-boosters-v2': boolean;
    'release-aggregated-booster-performance-improvements': boolean;
    'release-aggregated-keywords-insights-v2': boolean;
    'release-download-statistics-resume': boolean;
    'release-new-aggregated-semantic-analysis': boolean;
    'release-reviews-ai-settings-custom-prompt': boolean;
    'import-media-v2-toggle-enabled-by-default': boolean;
    'release-reviews-intelligent-subjects': boolean;
    'release-posts-v2-collaborators': boolean;
};
