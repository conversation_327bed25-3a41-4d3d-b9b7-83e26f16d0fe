import { GiftClaimStartDateOption, NextDrawEnabledDelay, WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const wheelOfFortuneJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    type: 'object',
    additionalProperties: false,
    title: 'WheelOfFortune',
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        giftIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
            default: [],
        },
        totemIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
            default: [],
        },
        parameters: {
            $ref: '#/definitions/Parameters',
        },
        startDate: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
        },
        endDate: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'createdAt', 'giftIds', 'parameters', 'totemIds', 'updatedAt'],
    definitions: {
        Parameters: {
            type: 'object',
            additionalProperties: false,
            properties: {
                primaryColor: {
                    type: 'string',
                },
                secondaryColor: {
                    type: 'string',
                },
                mediaId: {
                    anyOf: [
                        {
                            type: 'null',
                        },
                        {
                            type: 'string',
                            format: 'objectId',
                        },
                    ],
                },
                giftClaimStartDateOption: {
                    type: 'string',
                    enum: Object.values(GiftClaimStartDateOption),
                },
                giftClaimDurationInDays: {
                    type: 'integer',
                },
                redirectionSettings: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        nextDrawEnabledDelay: {
                            type: 'string',
                            enum: Object.values(NextDrawEnabledDelay),
                        },
                        shouldRedirect: {
                            type: 'boolean',
                        },
                        platforms: {
                            type: 'array',
                            items: {
                                type: 'object',
                                additionalProperties: false,
                                properties: {
                                    order: {
                                        type: 'integer',
                                    },
                                    platformKey: {
                                        type: 'string',
                                        enum: Object.values(WheelOfFortuneRedirectionPlatformKey),
                                    },
                                    privateReviewRatings: {
                                        type: 'array',
                                        items: {
                                            type: 'integer',
                                        },
                                        default: [],
                                    },
                                },
                                required: ['order', 'platformKey'],
                            },
                        },
                    },
                    required: ['nextDrawEnabledDelay', 'shouldRedirect', 'platforms'],
                },
            },
            required: ['primaryColor', 'secondaryColor', 'giftClaimDurationInDays', 'giftClaimStartDateOption', 'redirectionSettings'],
            title: 'Parameters',
        },
    },
} as const satisfies JSONSchemaExtraProps;
