import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { calendarEventJSONSchema } from './calendar-event-schema';

const calendarEventSchema = createMongooseSchemaFromJSONSchema(calendarEventJSONSchema);

// todo remove index in prod DB (and other envs)

export type ICalendarEvent = FromSchema<
    typeof calendarEventJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const CalendarEventModel = mongoose.model<ICalendarEvent>(calendarEventJSONSchema.title, calendarEventSchema);
