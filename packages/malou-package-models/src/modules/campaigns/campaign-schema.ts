import { CampaignType, ClientSource, ContactMode, emailRegex, PlatformKey, RedirectStar } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const campaignJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Campaign',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        startDate: {
            type: 'string',
            format: 'date-time',
        },
        endDate: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
            default: null,
        },
        name: {
            type: 'string',
        },
        contactMode: {
            enum: Object.values(ContactMode),
            default: ContactMode.EMAIL,
        },
        platformKey: {
            enum: Object.values(PlatformKey),
            default: PlatformKey.GMB,
        },
        type: {
            enum: Object.values(CampaignType),
            default: CampaignType.REVIEW_BOOSTER,
        },
        redirectFrom: {
            enum: Object.values(RedirectStar),
            default: RedirectStar.FOUR_STARS,
        },
        audience: {
            $ref: '#/definitions/Audience',
        },
        content: {
            $ref: '#/definitions/Content',
        },
        contactInteractions: {
            type: 'array',
            items: {
                $ref: '#/definitions/ContactInteraction',
            },
            default: [],
        },
        minDaysFromLastContactedAt: {
            type: 'string',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: [
        '_id',
        'audience',
        'contactInteractions',
        'contactMode',
        'content',
        'createdAt',
        'endDate',
        'name',
        'platformKey',
        'restaurantId',
        'startDate',
        'type',
        'updatedAt',
    ],
    definitions: {
        Audience: {
            type: 'object',
            additionalProperties: false,
            properties: {
                sources: {
                    type: 'array',
                    items: {
                        enum: Object.values(ClientSource),
                    },
                    default: [],
                },
                minDaysFromLastContactedAt: {
                    type: 'integer',
                    nullable: true,
                    default: 0,
                },
                startLastVisitedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                endLastVisitedAt: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
            },
            required: ['sources'],
            title: 'Audience',
        },
        ContactInteraction: {
            type: 'object',
            additionalProperties: false,
            properties: {
                clientId: {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Client',
                },
                lastStarRating: {
                    type: 'integer',
                    minimum: 1,
                    maximum: 5,
                    nullable: true,
                    default: null,
                },
                unsubscribedDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                    default: null,
                },
                openedDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                    default: null,
                },
                deliveredDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                    default: null,
                },
                lastStarRatingDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                    default: null,
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                awsMailId: {
                    type: 'string',
                },
            },
            required: ['_id', 'clientId'],
            title: 'ContactInteraction',
        },
        Content: {
            type: 'object',
            additionalProperties: false,
            properties: {
                from: {
                    $ref: '#/definitions/From',
                },
                object: {
                    type: 'string',
                },
                messageHTML: {
                    type: 'string',
                },
                _id: {
                    type: 'string',
                },
            },
            required: ['_id', 'from', 'messageHTML', 'object'],
            title: 'Content',
        },
        From: {
            type: 'object',
            additionalProperties: false,
            properties: {
                name: {
                    type: 'string',
                },
                email: {
                    type: 'string',
                    match: emailRegex,
                },
            },
            required: ['email', 'name'],
            title: 'From',
        },
    },
} as const satisfies JSONSchemaExtraProps;
