import { ApplicationLanguage, capitalize, ClientSource, ContactMode, emailRegex, PlatformKey } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const clientJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Client',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        firstName: {
            type: 'string',
            trim: true,
            set: capitalize,
        },
        lastName: {
            type: 'string',
            trim: true,
            set: capitalize,
        },
        birthday: {
            type: 'string',
            format: 'date-time',
        },
        address: {
            $ref: '#/definitions/Address',
        },
        email: {
            type: 'string',
            match: emailRegex,
        },
        source: {
            enum: Object.values(ClientSource),
        },
        lastVisitedAt: {
            type: 'string',
            format: 'date-time',
        },
        contactCount: {
            type: 'integer',
            default: 0,
        },
        visitCount: {
            type: 'integer',
            default: 1,
        },
        accepts: {
            type: 'array',
            items: {
                enum: Object.values(ContactMode),
            },
            default: [],
        },
        reviewsLeft: {
            type: 'array',
            items: {
                $ref: '#/definitions/ReviewsLeft',
            },
            default: [],
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        civility: {
            type: 'string',
        },
        phone: {
            $ref: '#/definitions/Phone',
        },
        language: {
            enum: [null, ...Object.values(ApplicationLanguage)],
        },
        lastContactedAt: {
            type: 'string',
            format: 'date-time',
        },
        duplicatedFromRestaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
    },
    required: ['_id', 'accepts', 'contactCount', 'createdAt', 'lastVisitedAt', 'restaurantId', 'source', 'updatedAt'],
    definitions: {
        Address: {
            type: 'object',
            additionalProperties: false,
            properties: {
                administrativeArea: {
                    type: 'string',
                    nullable: true,
                },
                locality: {
                    type: 'string',
                    nullable: true,
                },
                postalCode: {
                    type: 'string',
                    nullable: true,
                },
                country: {
                    type: 'string',
                    nullable: true,
                },
                formattedAddress: {
                    type: 'string',
                    nullable: true,
                },
                streetNumber: {
                    type: 'string',
                    nullable: true,
                },
                route: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
            title: 'Address',
        },
        Phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'integer',
                },
                digits: {
                    type: 'integer',
                },
            },
            required: ['digits', 'prefix'],
            title: 'Phone',
        },
        ReviewsLeft: {
            type: 'object',
            additionalProperties: false,
            properties: {
                platformKey: {
                    enum: [
                        PlatformKey.GMB,
                        PlatformKey.YELP,
                        PlatformKey.FOURSQUARE,
                        PlatformKey.TRIPADVISOR,
                        // To filter clients who left negative reviews later
                        'privateNegativeReview',
                    ],
                },
                hasLeftReview: {
                    type: 'boolean',
                    default: false,
                },
            },
            required: ['hasLeftReview', 'platformKey'],
            title: 'ReviewsLeft',
        },
    },
} as const satisfies JSONSchemaExtraProps;
