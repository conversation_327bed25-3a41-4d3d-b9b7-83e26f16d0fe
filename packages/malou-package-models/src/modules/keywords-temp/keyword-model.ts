import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { keywordJSONSchema } from './keyword-schema';

const keywordSchema = createMongooseSchemaFromJSONSchema(keywordJSONSchema);

keywordSchema.path('bricks').schema.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
});

keywordSchema.index({ apiLocationId: 1, text: 1, language: 1 }, { unique: true });
keywordSchema.index({ text: 1 });
keywordSchema.index({ lastVolumeFetchDate: 1 });

export type IKeywordTemp = FromSchema<
    typeof keywordJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const KeywordTempModel = mongoose.model<IKeywordTemp>(keywordJSONSchema.title, keywordSchema);
