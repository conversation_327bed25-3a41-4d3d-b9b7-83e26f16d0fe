import {
    Civility,
    CurrencyCode,
    isNotNil,
    MediaType,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
    ReviewAnalysisSentiment,
    SemanticAnalysisFetchStatus,
    UbereatsPromotionValue,
    urlRegex,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const reviewJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Review',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        platformId: {
            type: 'string',
            format: 'objectId',
        },
        socialId: {
            type: 'string',
        },
        archived: {
            type: 'boolean',
            default: false,
        },
        comments: {
            type: 'array',
            items: {
                $ref: '#/definitions/Comment',
            },
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        key: {
            enum: Object.values(PlatformKey),
        },
        rating: {
            type: 'number',
            nullable: true,
            default: null,
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
        },
        reviewer: {
            $ref: '#/definitions/Reviewer',
        },
        socialAttachments: {
            type: 'array',
            items: {
                $ref: '#/definitions/SocialAttachment',
            },
        },
        socialCreatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'socialSortDate is computed from this field and should be always updated if this field changes',
        },
        socialSortDate: {
            type: 'string',
            format: 'date-time',
            description:
                'This field exists to sort reviews efficiently on the last update date of the original review. This field is computed with the expression `socialUpdatedAt ?? socialCreatedAt`.',
        },
        socialRating: {
            type: 'number',
            nullable: true,
            default: null,
        },
        socialTranslatedText: {
            type: 'string',
            nullable: true,
            description: 'The `text` field translated by the platform (this is mostly specific to GMB)',
        },
        socialUpdatedAt: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
            description: 'socialSortDate is computed from this field and should be always updated if this field changes',
        },
        text: {
            type: 'string',
            nullable: true,
            description: 'the original text of the review written in the language specified by the field `lang`',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        wasAnsweredAutomatically: {
            type: 'boolean',
            default: false,
        },
        lang: {
            type: 'string',
            nullable: true,
            description: 'the language of the field `text` (and not `socialTranslatedText`)',
        },
        socialLink: {
            type: 'string',
            format: 'uri',
            match: urlRegex,
            nullable: true,
        },
        businessSocialLink: {
            type: 'string',
        },
        type: {
            description:
                'Only used on Facebook reviews. The two possible values are "rating" and "visitor_post" (but we no longer fetch visitor posts so recent Facebook reviews are always "rating"s).',
            type: 'string',
        },
        title: {
            type: 'string',
        },
        platformPresenceStatus: {
            enum: Object.values(PlatformPresenceStatus),
            default: PlatformPresenceStatus.FOUND,
        },
        keywordsLang: {
            type: 'string',
        },
        translationsId: {
            type: 'string',
            format: 'objectId',
        },
        responseStyle: {
            description: 'Response style used to generate the response to the review',
            anyOf: [
                {
                    type: 'null',
                },
                {
                    $ref: '#/definitions/ResponseStyle',
                },
            ],
            default: null,
        },
        matchedReviewsIds: {
            description: 'List of review ids that were used to generate the response to the review.',
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
        },
        reviewerNameValidation: {
            description: 'Validation results for the reviewer name fields.',
            anyOf: [
                {
                    type: 'null',
                },
                {
                    $ref: '#/definitions/ReviewerNameValidation',
                },
            ],
            default: null,
        },
        // Old discriminator ubereats fields below
        ratingTags: {
            type: 'array',
            items: {
                type: 'string',
            },
        },
        menuItemReviews: {
            type: 'array',
            items: {
                $ref: '#/definitions/MenuItemReview',
            },
        },
        eaterTotalOrders: {
            type: 'integer',
        },
        isReplyScheduled: {
            type: 'boolean',
        },
        order: {
            $ref: '#/definitions/Order',
        },
        semanticAnalysisFetchStatus: {
            description:
                'Indicates the status of the semantic analysis fetch : PENDING = message sent in the queue waiting to be treated, NO_RESULTS = empty array received from lambda, DONE = semantic analysis done, FAILED = error while fetching the semantic analysis',
            enum: [...Object.values(SemanticAnalysisFetchStatus), null],
        },
        aiRelevantBricks: {
            anyOf: [
                {
                    type: 'null',
                },
                {
                    type: 'array',
                    description:
                        'From the current list of selected bricks (keywords), list of ai returned bricks that are relevant to use in the response of the specific review. Contains generic bricks (like location, venue type, etc) AND related bricks, which are calculated by AI based on the review text',
                    items: {
                        $ref: '#/definitions/AiRelevantBrick',
                    },
                },
            ],
            default: null,
        },
        aiRelatedBricksCount: {
            description: 'Number of ai related bricks within the aiRelevantBricks array. Used for the keyword score lambda',
            type: 'integer',
        },
        // Old discriminator ubereats fields above
        intelligentSubjects: {
            description: 'List of intelligent subjects detected with IA in the review',
            type: 'array',
            items: {
                $ref: '#/definitions/IntelligentSubject',
            },
            default: [],
        },
    },
    required: [
        '_id',
        'archived',
        'comments',
        'createdAt',
        'key',
        'platformId',
        'restaurantId',
        'socialCreatedAt',
        'socialId',
        'socialSortDate',
        'updatedAt',
        'platformPresenceStatus',
    ],
    definitions: {
        Comment: {
            type: 'object',
            additionalProperties: false,
            properties: {
                text: {
                    type: 'string',
                },
                socialTranslatedText: {
                    type: 'string',
                    nullable: true,
                    description: 'The `text` field translated by the platform (this is mostly specific to GMB)',
                },
                socialUpdatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                posted: {
                    enum: Object.values(PostedStatus),
                    default: PostedStatus.PENDING,
                },
                templateIdUsed: {
                    anyOf: [
                        {
                            type: 'null',
                        },
                        {
                            type: 'string',
                            format: 'objectId',
                        },
                    ],
                    default: null,
                },
                retries: {
                    type: 'integer',
                    default: 0,
                },
                aiInteractionIdUsed: {
                    anyOf: [
                        {
                            type: 'null',
                        },
                        {
                            type: 'string',
                            format: 'objectId',
                        },
                    ],
                    default: null,
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                keywordAnalysis: {
                    $ref: '#/definitions/KeywordAnalysis',
                    nullable: true,
                },
                isMalou: {
                    description:
                        '`true` if the comment has been written from the Malou app, otherwise this field can be false, null or unset',
                    type: 'boolean',
                },
                author: {
                    description: 'author information when the comment has been written from the Malou app (when isMalou is true)',
                    $ref: '#/definitions/Author',
                    nullable: true,
                },
                socialId: {
                    type: 'string',
                },
                user: {
                    description: 'author information when the comment has been written on the external platform (when isMalou is not true)',
                    $ref: '#/definitions/User',
                },
                isRepliedFromAggregatedView: {
                    type: 'boolean',
                    default: 'false',
                },
                ubereatsPromotionValue: {
                    enum: Object.values(UbereatsPromotionValue),
                },
                ubereatsPromotionAmountInHundredths: {
                    type: 'number',
                },
            },
            required: ['_id', 'posted', 'text'],
            title: 'Comment',
        },
        Author: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                name: {
                    type: 'string',
                    nullable: true,
                },
                picture: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
            },
            required: [],
            title: 'Author',
        },
        KeywordAnalysis: {
            type: 'object',
            additionalProperties: false,
            properties: {
                keywords: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
                score: {
                    type: 'number',
                },
                count: {
                    type: 'integer',
                },
            },
            required: ['keywords'],
            title: 'KeywordAnalysis',
        },
        AiRelevantBrick: {
            type: 'object',
            additionalProperties: false,
            properties: {
                category: {
                    type: 'string',
                },
                text: {
                    type: 'string',
                },
                translationsId: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['text', 'category'],
            title: 'AiRelevantBrick',
        },
        User: {
            type: 'object',
            additionalProperties: false,
            properties: {
                socialId: {
                    type: 'string',
                    nullable: true,
                },
                displayName: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
            title: 'User',
        },
        Reviewer: {
            type: 'object',
            additionalProperties: false,
            properties: {
                profilePhotoUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                displayName: {
                    type: 'string',
                },
                socialId: {
                    type: 'string',
                    nullable: true,
                },
                socialUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                email: {
                    type: 'string',
                },
                doordashConsumerId: {
                    type: 'number',
                    nullable: true,
                    description: 'The consumer ID of the reviewer on DoorDash, used to reply to reviews',
                },
            },
            required: ['displayName'],
            title: 'Reviewer',
        },
        SocialAttachment: {
            type: 'object',
            additionalProperties: false,
            properties: {
                urls: {
                    $ref: '#/definitions/Urls',
                },
                type: {
                    enum: Object.values(MediaType),
                },
            },
            required: ['type', 'urls'],
            title: 'SocialAttachment',
        },
        Urls: {
            type: 'object',
            additionalProperties: false,
            properties: {
                original: {
                    type: 'string',
                },
                small: {
                    type: 'string',
                },
            },
            required: ['original'],
            title: 'Urls',
        },
        MenuItemReview: {
            type: 'object',
            additionalProperties: false,
            properties: {
                socialId: {
                    type: 'string',
                },
                rating: {
                    type: 'boolean',
                },
                name: {
                    type: 'string',
                },
                comment: {
                    type: 'string',
                    nullable: true,
                },
                tags: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
            },
            required: ['name', 'rating', 'socialId', 'tags'],
            title: 'MenuItemReview',
        },
        Order: {
            type: 'object',
            additionalProperties: false,
            properties: {
                workflowId: {
                    type: 'string',
                },
                deliveredAt: {
                    type: 'string',
                    format: 'date-time',
                },
                orderTotal: {
                    type: 'number',
                    nullable: true,
                },
                currencyCode: {
                    enum: Object.values(CurrencyCode),
                    nullable: true,
                },
                appVariant: {
                    type: 'string',
                },
            },
            required: ['appVariant', 'currencyCode', 'deliveredAt', 'workflowId'],
            title: 'Order',
        },
        IntelligentSubject: {
            type: 'object',
            additionalProperties: false,
            properties: {
                subject: {
                    type: 'string',
                },
                isDetected: {
                    type: 'boolean',
                },
                sentiment: {
                    enum: Object.values(ReviewAnalysisSentiment),
                },
            },
            required: ['subject', 'isDetected'],
            title: 'IntelligentSubject',
        },
        ResponseStyle: {
            type: 'object',
            additionalProperties: false,
            properties: {
                style: {
                    type: 'string',
                    description: 'The style of the response, e.g., "formal", "friendly", etc.',
                    validate: {
                        validator(v) {
                            return isNotNil(v);
                        },
                        message: (props) => `String should be defined, value: ${props.value}`,
                    },
                },
                toneOfVoice: {
                    type: 'string',
                    description: 'The tone of voice used in the response, e.g., "professional", "casual", etc.',
                    validate: {
                        validator(v) {
                            return isNotNil(v);
                        },
                        message: (props) => `String should be defined, value: ${props.value}`,
                    },
                },
                responseStructure: {
                    type: 'string',
                    description: 'The structure of the response, e.g., "bullet points", "paragraph", etc.',
                    validate: {
                        validator(v) {
                            return isNotNil(v);
                        },
                        message: (props) => `String should be defined, value: ${props.value}`,
                    },
                },
            },
            required: ['style', 'toneOfVoice', 'responseStructure'],
            title: 'ResponseStyle',
        },
        ReviewerNameValidation: {
            type: 'object',
            additionalProperties: false,
            properties: {
                gender: {
                    enum: Object.values(Civility),
                },
                firstName: {
                    type: 'string',
                },
                isFirstNameValid: {
                    type: 'boolean',
                },
                lastName: {
                    type: 'string',
                },
                isLastNameValid: {
                    type: 'boolean',
                },
            },
            required: ['gender', 'firstName', 'isFirstNameValid', 'lastName', 'isLastNameValid'],
            title: 'ReviewerNameValidation',
        },
    },
} as const satisfies JSONSchemaExtraProps;
