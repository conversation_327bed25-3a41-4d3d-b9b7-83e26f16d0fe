import { OverwriteOrAssign } from ':core/index';
import { IAttribute } from ':modules/attributes/attribute-model';
import { IKeywordTemp } from ':modules/keywords-temp/keyword-model';
import { IOrganization } from ':modules/organizations/organization-model';
import { IRestaurantKeyword } from ':modules/restaurant-keywords/restaurant-keyword-model';
import { IStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/store-locator-organization-config-model';

export type IStoreLocatorOrganizationConfigPopulated = OverwriteOrAssign<
    IStoreLocatorOrganizationConfig,
    {
        organization: IOrganization;
        aiSettings: IStoreLocatorOrganizationConfig['aiSettings'] & {
            attributes: IAttribute[];
            restaurantKeywords: OverwriteOrAssign<IRestaurantKeyword, { keyword: IKeywordTemp }>[];
        };
    }
>;
