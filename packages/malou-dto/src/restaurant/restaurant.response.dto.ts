import { BusinessCategory, PlatformKey, RestaurantCalendarEventsCountryType } from '@malou-io/package-utils';

import { MediaDto } from '../media/media.response.dto';
import { UserRestaurant } from '../user-restaurants/user-restaurants.response.dto';
import { DefaultModelProperties } from '../utils';
import { AddressDto } from './address/address.dto';
import { BookmarkedPostDto } from './bookmarkedPost/bookmarkedPost.dto';
import { CurrentStateDto } from './currentState/currentState.dto';
import { DescriptionDto } from './description/description.dto';
import { OtherHourDto, RegularHourDto, SpecialHourDto } from './hours/hours.dto';
import { LatlngDto } from './latlng/latlng.dto';
import { PhoneDto } from './phone/phone.dto';
import { PlatformAccessDto } from './platformAccess/platformAccess.dto';
import { SocialNetworkUrlsDto } from './social-networks/social-networks.dto';

export interface RestaurantResponseDto extends DefaultModelProperties {
    access: PlatformAccessDto[];
    active: boolean;
    address?: AddressDto;
    ai?: {
        monthlyCallCount?: number;
        callCount?: number;
    };
    availableHoursTypeIds?: string[];
    reservationUrl?: string;
    bookmarkedPosts: BookmarkedPostDto[];
    boosterPack: {
        activated?: boolean;
        activationDate: Date | null;
    };
    bricks: string[];
    bricksPostalCode?: string;
    calendarEvents: string[];
    calendarEventsCountry?: RestaurantCalendarEventsCountryType;
    category?: string;
    categoryList: string[];
    commentsLastUpdate?: string;
    cover?: string;
    coverChanged: boolean;
    coverPopulated?: MediaDto;
    currentState?: CurrentStateDto;
    descriptions: DescriptionDto[];
    email?: string;
    internalName?: string;
    isClaimed?: boolean;
    isClosedTemporarily: boolean;
    keywordToolApiLocationId?: string;
    latlng?: LatlngDto;
    logo?: string;
    logoChanged: boolean;
    logoPopulated?: MediaDto;
    menuUrl?: string;
    name: string;
    openingDate?: string;
    orderUrl?: string;
    organizationId: string;
    otherHours?: OtherHourDto[];
    phone?: PhoneDto;
    placeId: string;
    regularHours?: RegularHourDto[];
    relatedUrls: (null | string)[];
    reviewsLastUpdate?: string;
    socialId?: string;
    socialNetworkUrls: SocialNetworkUrlsDto;
    specialHours: SpecialHourDto[];
    toolboxMenuUrl?: string;
    totemDisplayName?: { title?: string; text?: string };
    type: BusinessCategory;
    uniqueKey: string;
    website?: string;
}

export type SearchRestaurantsByTextResponseDto = Pick<RestaurantResponseDto, 'id' | 'name' | '_id'>;

export type AddUserToRestaurantWithEmailResponseDto = UserRestaurant;

export type GetUserWithEmailRestaurantsResponseDto = Pick<UserRestaurant, '_id' | 'restaurantId'>;

export type CreateRestaurantResponseDto = { restaurantId: string };

export type GetRestaurantCurrentStateResponseDto = Pick<RestaurantResponseDto, 'id' | 'currentState' | '_id'>;

export type GetManagersResponseDto = {
    _id: string;
    email: string;
    name: string;
    lastname: string;
    role: string;
    profilePicture?: string;
};

export type LightRestaurantDto = {
    id: string;
    name: string;
    internalName?: string;
    address?: AddressDto;
    logo: MediaDto | null;
};

export type LightRestaurantWithBoosterPackDto = LightRestaurantDto & {
    boosterPack?: {
        activated: boolean;
        activationDate: string | null;
    };
};

export type RestaurantWithoutStickerDto = {
    id: string;
    _id: string;
    name: string;
    internalName?: string;
    address?: AddressDto;
    type: BusinessCategory;
};

type UpdateActionDetails = {
    success: boolean;
    restaurantsIds: string[];
};

export type UpdateRestaurantsForUserDto = {
    add: UpdateActionDetails;
    update: UpdateActionDetails;
    remove: UpdateActionDetails;
};

export interface StoreLocatorOrganizationRestaurantDto {
    id: string;
    name: string;
    internalName?: string;
    address?: AddressDto;
    type: BusinessCategory;
    attributeList?: Array<{
        id: string;
        priority: number;
        attributeId: string;
        platformKey: PlatformKey;
        attributeName: {
            fr: string;
            en?: string;
            es?: string;
            it?: string;
        };
    }>;
}
