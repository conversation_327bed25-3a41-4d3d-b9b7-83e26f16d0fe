import { PhoneDto } from '../restaurant';

export interface LightClientAddressDto {
    locality?: string | null;
    country?: string | null;
    postalCode?: string | null;
    formattedAddress?: string | null;
}

export interface LightClientDto {
    id: string;
    civility?: string;
    firstName?: string;
    lastName?: string;
    address?: LightClientAddressDto;
    phone?: PhoneDto;
    email?: string;
    language?: string | null;
}
